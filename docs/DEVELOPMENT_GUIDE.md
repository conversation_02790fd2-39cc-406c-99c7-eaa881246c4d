# 🛠️ Development Guide - MCP Multi-Agent

## Overview

This guide provides comprehensive information for developers who want to contribute to or extend the MCP Multi-Agent project. It covers development setup, coding standards, testing procedures, and contribution guidelines.

## 🚀 Development Setup

### Prerequisites

- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 8.0.0 or higher
- **Git**: For version control
- **VS Code**: Recommended IDE with TypeScript support

### Initial Setup

1. **Clone and setup:**
```bash
git clone https://github.com/user/mcp-multi-agent.git
cd mcp-multi-agent
npm install
```

2. **Environment configuration:**
```bash
cp .env.example .env
# Add your OpenAI API key to .env
```

### Environment Configuration System

The project uses a comprehensive environment configuration system with validation and type safety:

#### Configuration Architecture

```
src/config/
├── env.ts              # Environment configuration with validation
├── loader.ts           # Main configuration loader
├── types.ts            # TypeScript interfaces
├── client-factory.ts   # MCP client factory
└── test-env.ts         # Environment testing utilities
```

#### Environment Variables

**Required Variables:**
- `OPENAI_API_KEY`: Your OpenAI API key (required for LLM integration)

**Optional Configuration:**
- `OPENAI_MODEL`: Model to use (default: gpt-4o)
- `OPENAI_TEMPERATURE`: Response temperature 0-2 (default: 0.1)
- `OPENAI_MAX_TOKENS`: Maximum tokens 1-32000 (default: 4096)
- `OPENAI_MAX_RETRIES`: Retry attempts 0-10 (default: 3)
- `OPENAI_RETRY_DELAY`: Retry delay 100-30000ms (default: 2000)
- `AGENT_MAX_STEPS`: Agent steps 1-100 (default: 10)
- `AGENT_TIMEOUT`: Agent timeout 1000-300000ms (default: 60000)
- `MAX_CONCURRENT_SERVERS`: Concurrent servers 1-20 (default: 3)
- `SERVER_STARTUP_TIMEOUT`: Startup timeout 5-300s (default: 30)
- `LOG_LEVEL`: Logging level debug|info|warn|error (default: info)
- `LOG_FORMAT`: Log format text|json (default: text)
- `LOG_FILE`: Optional log file path
- `NODE_ENV`: Environment development|production|test (default: development)

#### Environment Testing

Test your environment configuration:

```bash
# Test environment configuration
npm run dev:test-env

# Test with specific environment
NODE_ENV=production npm run dev:test-env

# Test agent with environment
npm run dev test-agent --minimal
```

#### Environment Validation Features

- **Type Safety**: Full TypeScript interfaces for all environment variables
- **Range Validation**: Numeric values validated against min/max ranges
- **Enum Validation**: String values validated against allowed options
- **Helpful Errors**: Detailed error messages with suggestions
- **Default Values**: Sensible defaults for all optional variables
- **Environment Detection**: Automatic development/production/test detection

3. **Verify setup:**
```bash
npm run type-check
npm run build
npm run dev test-openai
```

### Development Workflow

```bash
# Start development with hot reload
npm run dev

# Type checking (runs continuously)
npm run type-check

# Build for production
npm run build

# Run tests
npm run test
npm run dev test-agent

# Linting
npm run lint
npm run lint:fix
```

## 📁 Project Structure

```
mcp-multi-agent/
├── src/                    # Source code
│   ├── agent/             # Agent implementation ✅
│   ├── config/            # Configuration management ✅
│   ├── llm/               # LLM integration ✅
│   ├── cli/               # CLI interface (planned)
│   ├── monitoring/        # Health monitoring (planned)
│   ├── utils/             # Utility functions (planned)
│   └── index.ts           # Main entry point
├── docs/                  # Documentation
├── dist/                  # Build output
├── tests/                 # Test files (planned)
├── examples/              # Usage examples (planned)
└── scripts/               # Build and utility scripts
```

## 🔧 Coding Standards

### TypeScript Configuration

The project uses strict TypeScript configuration:

```json
{
  "compilerOptions": {
    "strict": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true
  }
}
```

### Code Style Guidelines

1. **Type Safety**: Always use explicit types, avoid `any`
2. **Error Handling**: Comprehensive error handling with descriptive messages
3. **Documentation**: JSDoc comments for all public APIs
4. **Naming**: Use descriptive names, follow TypeScript conventions
5. **Imports**: Use path aliases (`@/config`, `@/llm`, etc.)

### Example Code Pattern

```typescript
/**
 * Example function with proper TypeScript patterns
 */
export async function exampleFunction(
  config: RequiredConfig,
  options?: OptionalOptions
): Promise<Result> {
  try {
    // Validate inputs
    if (!config.apiKey) {
      throw new Error('API key is required');
    }

    // Implementation with proper error handling
    const result = await someAsyncOperation(config, options);
    
    return {
      success: true,
      data: result,
      timestamp: Date.now()
    };
  } catch (error) {
    throw new Error(`Operation failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}
```

## 🧪 Testing Strategy

### Test Structure

```
tests/
├── unit/              # Unit tests for individual components
├── integration/       # Integration tests for component interaction
├── e2e/              # End-to-end tests for complete workflows
└── fixtures/         # Test data and mock configurations
```

### Testing Commands

```bash
# Run all tests
npm test

# Run specific test suites
npm run dev test-openai      # OpenAI integration
npm run dev test-agent       # Agent implementation
npm run dev test-agent --minimal  # Minimal agent test

# Run with coverage
npm run test:coverage
```

### Writing Tests

Example test pattern:

```typescript
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createMultiServerAgent } from '@/agent';
import { loadConfig } from '@/config';
import { getOpenAIClient } from '@/llm';

describe('MultiServerAgent', () => {
  let agent: MultiServerAgent;
  
  beforeEach(async () => {
    const config = loadConfig({ /* test config */ });
    const openaiClient = await getOpenAIClient();
    agent = await createMultiServerAgent(config, openaiClient);
  });

  afterEach(async () => {
    await agent.shutdown();
  });

  it('should initialize successfully', () => {
    expect(agent.isReady()).toBe(true);
  });

  it('should handle queries correctly', async () => {
    const result = await agent.run('Test query');
    expect(result.response).toBeDefined();
    expect(result.executionTime).toBeGreaterThan(0);
  });
});
```

## 🏗️ Architecture Patterns

### Factory Pattern

Used for creating clients and agents:

```typescript
// LLM Factory
export class LLMFactory {
  private static instance: LLMFactory;
  private clients = new Map<string, OpenAIClient>();

  static getInstance(): LLMFactory {
    if (!LLMFactory.instance) {
      LLMFactory.instance = new LLMFactory();
    }
    return LLMFactory.instance;
  }
}

// Agent Factory
export async function createMultiServerAgent(
  config: MCPMultiAgentConfig,
  openaiClient: OpenAIClient
): Promise<MultiServerAgent> {
  const agent = new MultiServerAgent(config, openaiClient);
  if (config.agent.autoInitialize) {
    await agent.initialize();
  }
  return agent;
}
```

### Configuration Pattern

Centralized configuration with environment variable support:

```typescript
export function loadConfig(customConfig?: Partial<MCPMultiAgentConfig>): MCPMultiAgentConfig {
  // Load from environment
  const envConfig = loadFromEnvironment();
  
  // Merge with defaults and custom config
  const config = {
    ...DEFAULT_CONFIG,
    ...envConfig,
    ...customConfig
  };
  
  // Validate configuration
  validateConfig(config);
  
  return config;
}
```

### Error Handling Pattern

Consistent error handling across the codebase:

```typescript
export class ConfigValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ConfigValidationError';
  }
}

// Usage
try {
  const config = loadConfig();
} catch (error) {
  if (error instanceof ConfigValidationError) {
    console.error('Configuration error:', error.message);
  } else {
    console.error('Unexpected error:', error);
  }
}
```

## 🔌 Adding New Features

### Adding a New MCP Server

1. **Define server configuration:**
```typescript
// In src/config/examples.ts
export const NEW_SERVER_CONFIG: MCPServerConfig = {
  id: 'new-server',
  name: 'New Server',
  description: 'Description of the new server',
  connectionType: 'stdio',
  command: 'npx',
  args: ['new-server-package'],
  enabled: false,
  priority: 5,
  tags: ['new', 'server'],
  retry: { maxAttempts: 3, delayMs: 1000 }
};
```

2. **Add to default configuration:**
```typescript
// In src/config/types.ts
export const DEFAULT_CONFIG = {
  // ... existing config
  servers: [
    // ... existing servers
    NEW_SERVER_CONFIG
  ]
};
```

3. **Test the integration:**
```typescript
// Create test for new server
const config = createConfig([NEW_SERVER_CONFIG]);
const agent = await createMultiServerAgent(config, openaiClient);
const result = await agent.testConnections();
```

### Adding New Agent Capabilities

1. **Extend AgentRunOptions:**
```typescript
export interface AgentRunOptions {
  // ... existing options
  newOption?: boolean;
}
```

2. **Implement in MultiServerAgent:**
```typescript
async run(query: string, options: AgentRunOptions = {}): Promise<AgentResult> {
  // Handle new option
  if (options.newOption) {
    // New capability implementation
  }
  
  // ... existing implementation
}
```

3. **Add tests:**
```typescript
it('should handle new option', async () => {
  const result = await agent.run('Test query', { newOption: true });
  // Assertions for new behavior
});
```

## 📦 Building and Deployment

### Build Process

```bash
# Clean build
npm run clean
npm run build

# Verify build
node dist/index.js --help
```

### Build Configuration

The project uses TypeScript compiler with these key settings:

```json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "Node",
    "outDir": "./dist",
    "rootDir": "./src",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  }
}
```

### Distribution

```bash
# Create distribution package
npm pack

# Install from package
npm install mcp-multi-agent-1.0.0.tgz
```

## 🐛 Debugging

### Debug Configuration

VS Code launch configuration:

```json
{
  "type": "node",
  "request": "launch",
  "name": "Debug MCP Agent",
  "program": "${workspaceFolder}/src/index.ts",
  "args": ["test-agent", "--minimal"],
  "runtimeArgs": ["-r", "tsx/cjs"],
  "env": {
    "NODE_ENV": "development"
  },
  "console": "integratedTerminal"
}
```

### Logging

Enable debug logging:

```typescript
const config = loadConfig({
  logging: {
    level: 'debug',
    format: 'text'
  }
});
```

### Common Debug Scenarios

1. **Agent initialization issues:**
```bash
npm run dev test-agent --minimal
```

2. **OpenAI connection problems:**
```bash
npm run dev test-openai
```

3. **MCP server connection issues:**
```bash
# Enable verbose logging
DEBUG=mcp-use:* npm run dev test-agent
```

## 🤝 Contributing

### Contribution Workflow

1. **Fork the repository**
2. **Create a feature branch:**
```bash
git checkout -b feature/new-feature
```

3. **Make changes following coding standards**
4. **Add tests for new functionality**
5. **Run the test suite:**
```bash
npm run type-check
npm run lint
npm run test
npm run dev test-agent
```

6. **Commit with descriptive messages:**
```bash
git commit -m "feat: add new MCP server integration"
```

7. **Push and create pull request**

### Commit Message Format

Follow conventional commits:

```
feat: add new feature
fix: resolve bug in agent initialization
docs: update API documentation
test: add integration tests
refactor: improve error handling
```

### Code Review Checklist

- [ ] TypeScript strict mode compliance
- [ ] Comprehensive error handling
- [ ] Unit tests for new functionality
- [ ] Integration tests where applicable
- [ ] Documentation updates
- [ ] No breaking changes (or properly documented)
- [ ] Performance considerations addressed

## 📋 Release Process

### Version Management

```bash
# Update version
npm version patch|minor|major

# Build and test
npm run build
npm run test

# Publish (when ready)
npm publish
```

### Release Checklist

- [ ] All tests passing
- [ ] Documentation updated
- [ ] CHANGELOG.md updated
- [ ] Version bumped appropriately
- [ ] Build artifacts verified
- [ ] Security review completed

## 🔒 Security Guidelines

### API Key Management

- Never commit API keys to version control
- Use environment variables for sensitive data
- Rotate keys regularly
- Monitor API usage and costs

### Code Security

- Validate all inputs
- Use TypeScript strict mode
- Handle errors gracefully
- Audit dependencies regularly

```bash
# Security audit
npm audit
npm audit fix
```

## 📚 Resources

### Documentation

- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [mcp-use Library](https://github.com/mcp-use/mcp-use)
- [OpenAI API Documentation](https://platform.openai.com/docs)
- [Vitest Testing Framework](https://vitest.dev/)

### Tools

- [VS Code](https://code.visualstudio.com/) - Recommended IDE
- [TypeScript ESLint](https://typescript-eslint.io/) - Linting
- [Prettier](https://prettier.io/) - Code formatting
- [Vitest](https://vitest.dev/) - Testing framework

---

*Last Updated: 2025-08-17*  
*Version: 1.0*  
*For questions about development, see the [User Guide](./USER_GUIDE.md) or [API Reference](./API_REFERENCE.md)*
