import type { z as z3 } from "zod/v3";
import { CallbackManagerForToolRun } from "../callbacks/manager.js";
import type { BaseLangChainParams, ToolDefinition } from "../language_models/base.js";
import type { RunnableConfig } from "../runnables/config.js";
import { RunnableToolLike, type RunnableInterface } from "../runnables/base.js";
import { type DirectToolOutput, type ToolCall, type ToolMessage } from "../messages/tool.js";
import type { MessageContent } from "../messages/base.js";
import { type InferInteropZodInput, type InferInteropZodOutput, type InteropZodType } from "../utils/types/zod.js";
import { JSONSchema } from "../utils/json_schema.js";
export type ResponseFormat = "content" | "content_and_artifact" | string;
export type ToolOutputType = any;
export type ContentAndArtifact = [MessageContent, any];
/**
 * Conditional type that determines the return type of the {@link StructuredTool.invoke} method.
 * - If the input is a ToolCall, it returns a ToolMessage
 * - If the config is a runnable config and contains a toolCall property, it returns a ToolMessage
 * - Otherwise, it returns the original output type
 */
export type ToolReturnType<TInput, TConfig, TOutput> = TOutput extends DirectToolOutput ? TOutput : TConfig extends {
    toolCall: {
        id: string;
    };
} ? ToolMessage : TConfig extends {
    toolCall: {
        id: undefined;
    };
} ? TOutput : TConfig extends {
    toolCall: {
        id?: string;
    };
} ? TOutput | ToolMessage : TInput extends ToolCall ? ToolMessage : TOutput;
/**
 * Base type that establishes the types of input schemas that can be used for LangChain tool
 * definitions.
 */
export type ToolInputSchemaBase = z3.ZodTypeAny | JSONSchema;
/**
 * Parameters for the Tool classes.
 */
export interface ToolParams extends BaseLangChainParams {
    /**
     * The tool response format.
     *
     * If "content" then the output of the tool is interpreted as the contents of a
     * ToolMessage. If "content_and_artifact" then the output is expected to be a
     * two-tuple corresponding to the (content, artifact) of a ToolMessage.
     *
     * @default "content"
     */
    responseFormat?: ResponseFormat;
    /**
     * Whether to show full details in the thrown parsing errors.
     *
     * @default false
     */
    verboseParsingErrors?: boolean;
}
export type ToolRunnableConfig<ConfigurableFieldType extends Record<string, any> = Record<string, any>> = RunnableConfig<ConfigurableFieldType> & {
    toolCall?: ToolCall;
};
/**
 * Schema for defining tools.
 *
 * @version 0.2.19
 */
export interface StructuredToolParams extends Pick<StructuredToolInterface, "name" | "schema"> {
    /**
     * An optional description of the tool to pass to the model.
     */
    description?: string;
}
/**
 * Utility type that resolves the output type of a tool input schema.
 *
 * Input & Output types are a concept used with Zod schema, as Zod allows for transforms to occur
 * during parsing. When using JSONSchema, input and output types are the same.
 *
 * The input type for a given schema should match the structure of the arguments that the LLM
 * generates as part of its {@link ToolCall}. The output type will be the type that results from
 * applying any transforms defined in your schema. If there are no transforms, the input and output
 * types will be the same.
 */
export type ToolInputSchemaOutputType<T> = T extends InteropZodType ? InferInteropZodOutput<T> : T extends JSONSchema ? unknown : never;
/**
 * Utility type that resolves the input type of a tool input schema.
 *
 * Input & Output types are a concept used with Zod schema, as Zod allows for transforms to occur
 * during parsing. When using JSONSchema, input and output types are the same.
 *
 * The input type for a given schema should match the structure of the arguments that the LLM
 * generates as part of its {@link ToolCall}. The output type will be the type that results from
 * applying any transforms defined in your schema. If there are no transforms, the input and output
 * types will be the same.
 */
export type ToolInputSchemaInputType<T> = T extends InteropZodType ? InferInteropZodInput<T> : T extends JSONSchema ? unknown : never;
/**
 * Defines the type that will be passed into a tool handler function as a result of a tool call.
 *
 * @param SchemaT - The type of the tool input schema. Usually you don't need to specify this.
 * @param SchemaInputT - The TypeScript type representing the structure of the tool arguments generated by the LLM. Useful for type checking tool handler functions when using JSONSchema.
 */
export type StructuredToolCallInput<SchemaT = ToolInputSchemaBase, SchemaInputT = ToolInputSchemaInputType<SchemaT>> = (ToolInputSchemaOutputType<SchemaT> extends string ? string : never) | SchemaInputT | ToolCall;
/**
 * An input schema type for tools that accept a single string input.
 *
 * This schema defines a tool that takes an optional string parameter named "input".
 * It uses Zod's effects to transform the input and strip any extra properties.
 *
 * This is primarily used for creating simple string-based tools where the LLM
 * only needs to provide a single text value as input to the tool.
 */
export type StringInputToolSchema = z3.ZodType<string | undefined, z3.ZodTypeDef, any>;
/**
 * Defines the type for input to a tool's call method.
 *
 * This type is a convenience alias for StructuredToolCallInput with the input type
 * derived from the schema. It represents the possible inputs that can be passed to a tool,
 * which can be either:
 * - A string (if the tool accepts string input)
 * - A structured input matching the tool's schema
 * - A ToolCall object (typically from an LLM)
 *
 * @param SchemaT - The schema type for the tool input, defaults to StringInputToolSchema
 */
export type ToolCallInput<SchemaT = StringInputToolSchema> = StructuredToolCallInput<SchemaT, ToolInputSchemaInputType<SchemaT>>;
/**
 * Interface that defines the shape of a LangChain structured tool.
 *
 * A structured tool is a tool that uses a schema to define the structure of the arguments that the
 * LLM generates as part of its {@link ToolCall}.
 *
 * @param SchemaT - The type of the tool input schema. Usually you don't need to specify this.
 * @param SchemaInputT - The TypeScript type representing the structure of the tool arguments generated by the LLM. Useful for type checking tool handler functions when using JSONSchema.
 */
export interface StructuredToolInterface<SchemaT = ToolInputSchemaBase, SchemaInputT = ToolInputSchemaInputType<SchemaT>, ToolOutputT = ToolOutputType> extends RunnableInterface<StructuredToolCallInput<SchemaT, SchemaInputT>, ToolOutputT | ToolMessage> {
    lc_namespace: string[];
    /**
     * A Zod schema representing the parameters of the tool.
     */
    schema: SchemaT;
    /**
     * Invokes the tool with the provided argument and configuration.
     * @param arg The input argument for the tool.
     * @param configArg Optional configuration for the tool call.
     * @returns A Promise that resolves with the tool's output.
     */
    invoke<TArg extends StructuredToolCallInput<SchemaT, SchemaInputT>, TConfig extends ToolRunnableConfig | undefined>(arg: TArg, configArg?: TConfig): Promise<ToolReturnType<TArg, TConfig, ToolOutputT>>;
    /**
     * @deprecated Use .invoke() instead. Will be removed in 0.3.0.
     *
     * Calls the tool with the provided argument, configuration, and tags. It
     * parses the input according to the schema, handles any errors, and
     * manages callbacks.
     * @param arg The input argument for the tool.
     * @param configArg Optional configuration or callbacks for the tool.
     * @param tags Optional tags for the tool.
     * @returns A Promise that resolves with a string.
     */
    call<TArg extends StructuredToolCallInput<SchemaT, SchemaInputT>, TConfig extends ToolRunnableConfig | undefined>(arg: TArg, configArg?: TConfig, 
    /** @deprecated */
    tags?: string[]): Promise<ToolReturnType<TArg, TConfig, ToolOutputT>>;
    /**
     * The name of the tool.
     */
    name: string;
    /**
     * A description of the tool.
     */
    description: string;
    /**
     * Whether to return the tool's output directly.
     *
     * Setting this to true means that after the tool is called,
     * an agent should stop looping.
     */
    returnDirect: boolean;
}
/**
 * A special interface for tools that accept a string input, usually defined with the {@link Tool} class.
 *
 * @param SchemaT - The type of the tool input schema. Usually you don't need to specify this.
 * @param SchemaInputT - The TypeScript type representing the structure of the tool arguments generated by the LLM. Useful for type checking tool handler functions when using JSONSchema.
 */
export interface ToolInterface<SchemaT = StringInputToolSchema, SchemaInputT = ToolInputSchemaInputType<SchemaT>, ToolOutputT = ToolOutputType> extends StructuredToolInterface<SchemaT, SchemaInputT, ToolOutputT> {
    /**
     * @deprecated Use .invoke() instead. Will be removed in 0.3.0.
     *
     * Calls the tool with the provided argument and callbacks. It handles
     * string inputs specifically.
     * @param arg The input argument for the tool, which can be a string, undefined, or an input of the tool's schema.
     * @param callbacks Optional callbacks for the tool.
     * @returns A Promise that resolves with a string.
     */
    call<TArg extends StructuredToolCallInput<SchemaT, SchemaInputT>, TConfig extends ToolRunnableConfig | undefined>(arg: TArg, callbacks?: TConfig): Promise<ToolReturnType<NonNullable<TArg>, TConfig, ToolOutputT>>;
}
/**
 * Base interface for the input parameters of the {@link DynamicTool} and
 * {@link DynamicStructuredTool} classes.
 */
export interface BaseDynamicToolInput extends ToolParams {
    name: string;
    description: string;
    /**
     * Whether to return the tool's output directly.
     *
     * Setting this to true means that after the tool is called,
     * an agent should stop looping.
     */
    returnDirect?: boolean;
}
/**
 * Interface for the input parameters of the DynamicTool class.
 */
export interface DynamicToolInput<ToolOutputT = ToolOutputType> extends BaseDynamicToolInput {
    func: (input: string, runManager?: CallbackManagerForToolRun, config?: ToolRunnableConfig) => Promise<ToolOutputT>;
}
/**
 * Interface for the input parameters of the DynamicStructuredTool class.
 *
 * @param SchemaT - The type of the tool input schema. Usually you don't need to specify this.
 * @param SchemaOutputT - The TypeScript type representing the result of applying the schema to the tool arguments. Useful for type checking tool handler functions when using JSONSchema.
 */
export interface DynamicStructuredToolInput<SchemaT = ToolInputSchemaBase, SchemaOutputT = ToolInputSchemaOutputType<SchemaT>, ToolOutputT = ToolOutputType> extends BaseDynamicToolInput {
    /**
     * Tool handler function - the function that will be called when the tool is invoked.
     *
     * @param input - The input to the tool.
     * @param runManager - The run manager for the tool.
     * @param config - The configuration for the tool.
     * @returns The result of the tool.
     */
    func: (input: SchemaOutputT, runManager?: CallbackManagerForToolRun, config?: RunnableConfig) => Promise<ToolOutputT>;
    schema: SchemaT;
}
/**
 * Confirm whether the inputted tool is an instance of `StructuredToolInterface`.
 *
 * @param {StructuredToolInterface | JSONSchema | undefined} tool The tool to check if it is an instance of `StructuredToolInterface`.
 * @returns {tool is StructuredToolInterface} Whether the inputted tool is an instance of `StructuredToolInterface`.
 */
export declare function isStructuredTool(tool?: StructuredToolInterface | ToolDefinition | JSONSchema): tool is StructuredToolInterface;
/**
 * Confirm whether the inputted tool is an instance of `RunnableToolLike`.
 *
 * @param {unknown | undefined} tool The tool to check if it is an instance of `RunnableToolLike`.
 * @returns {tool is RunnableToolLike} Whether the inputted tool is an instance of `RunnableToolLike`.
 */
export declare function isRunnableToolLike(tool?: unknown): tool is RunnableToolLike;
/**
 * Confirm whether or not the tool contains the necessary properties to be considered a `StructuredToolParams`.
 *
 * @param {unknown | undefined} tool The object to check if it is a `StructuredToolParams`.
 * @returns {tool is StructuredToolParams} Whether the inputted object is a `StructuredToolParams`.
 */
export declare function isStructuredToolParams(tool?: unknown): tool is StructuredToolParams;
/**
 * Whether or not the tool is one of StructuredTool, RunnableTool or StructuredToolParams.
 * It returns `is StructuredToolParams` since that is the most minimal interface of the three,
 * while still containing the necessary properties to be passed to a LLM for tool calling.
 *
 * @param {unknown | undefined} tool The tool to check if it is a LangChain tool.
 * @returns {tool is StructuredToolParams} Whether the inputted tool is a LangChain tool.
 */
export declare function isLangChainTool(tool?: unknown): tool is StructuredToolParams;
