/**
 * Configuration loader for MCP multi-server agent
 */
import type { MCPMultiAgentConfig, MCPServerConfig, LLMConfig, AgentConfig, ServerManagerConfig } from './types.js';
/**
 * Configuration validation error
 */
export declare class ConfigValidationError extends Error {
    constructor(message: string);
}
/**
 * Load configuration from environment variables and defaults
 */
export declare function loadConfig(customConfig?: Partial<MCPMultiAgentConfig>): MCPMultiAgentConfig;
/**
 * Create a configuration with custom servers
 */
export declare function createConfig(servers: MCPServerConfig[], options?: {
    llm?: Partial<LLMConfig>;
    agent?: Partial<AgentConfig>;
    serverManager?: Partial<ServerManagerConfig>;
}): MCPMultiAgentConfig;
//# sourceMappingURL=loader.d.ts.map