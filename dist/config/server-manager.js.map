{"version": 3, "file": "server-manager.js", "sourceRoot": "", "sources": ["../../src/config/server-manager.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AA2D5C;;GAEG;AACH,MAAM,OAAO,aAAa;IAChB,MAAM,CAA8B;IACpC,OAAO,GAA+B,IAAI,GAAG,EAAE,CAAC;IAChD,eAAe,GAAuB,IAAI,GAAG,EAAE,CAAC;IAChD,aAAa,GAAiD,IAAI,GAAG,EAAE,CAAC;IACxE,mBAAmB,GAA0B,IAAI,CAAC;IAE1D,YAAY,MAAmC;QAC7C,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,cAAc,CAAC,aAAa;YAC/B,GAAG,MAAM;YACT,cAAc,EAAE;gBACd,uBAAuB,EAAE,CAAC;gBAC1B,WAAW,EAAE,MAAM,EAAE,YAAY;gBACjC,iBAAiB,EAAE,KAAK,EAAE,aAAa;gBACvC,GAAG,MAAM,CAAC,cAAc;aACzB;YACD,kBAAkB,EAAE;gBAClB,kBAAkB,EAAE,GAAG,EAAE,SAAS;gBAClC,YAAY,EAAE,GAAG,EAAE,MAAM;gBACzB,UAAU,EAAE,KAAK,EAAE,WAAW;gBAC9B,GAAG,MAAM,CAAC,kBAAkB;aAC7B;YACD,aAAa,EAAE;gBACb,QAAQ,EAAE,gBAAgB;gBAC1B,GAAG,MAAM,CAAC,aAAa;aACxB;YACD,cAAc,EAAE;gBACd,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,KAAK,EAAE,aAAa;gBACrC,gBAAgB,EAAE,CAAC;gBACnB,GAAG,MAAM,CAAC,cAAc;aACzB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB;QAMf,MAAM,MAAM,GAAQ;YAClB,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YACrC,uCAAuC;YACvC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YAC5D,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC9C,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;SACzC,CAAC;QAEF,mDAAmD;QACnD,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YACnD,MAAM,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;QACjE,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YACnD,MAAM,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;QACjE,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,OAA0B;QACzC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAEjD,qCAAqC;QACrC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;oBAC1B,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,eAAe,EAAE,CAAC;oBAClB,mBAAmB,EAAE,CAAC;oBACtB,SAAS,EAAE,CAAC;oBACZ,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,eAAe,EAAE,IAAI,IAAI,EAAE;oBAC3B,MAAM,EAAE,cAAc;iBACvB,CAAC,CAAC;gBAEH,mCAAmC;gBACnC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAE5C,6BAA6B;gBAC7B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;YACpE,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,UAAU,CAAC,CAAC;IACnG,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAChD,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAEpC,OAAO,CAAC,GAAG,CAAC,2CAA2C,IAAI,CAAC,MAAM,CAAC,mBAAmB,KAAK,CAAC,CAAC;IAC/F,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;YACtE,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAC3C,IAAI,CAAC,OAAO;oBAAE,OAAO;gBAErB,gCAAgC;gBAChC,OAAO,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;gBAErC,8BAA8B;gBAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACtD,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;oBAC5B,uCAAuC;oBACvC,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;oBAC1E,IAAI,kBAAkB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,eAAe,IAAI,KAAK,CAAC,EAAE,CAAC;wBAChF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;wBAC9C,OAAO,CAAC,GAAG,CAAC,0BAA0B,QAAQ,2BAA2B,CAAC,CAAC;oBAC7E,CAAC;gBACH,CAAC;gBAED,+EAA+E;gBAC/E,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE;oBACjC,MAAM,EAAE,SAAS,EAAE,6CAA6C;oBAChE,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC;iBACjE,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBACtE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAAgB,EAAE,OAA+B;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,QAAgB,EAAE,MAAW;QACrD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,oBAAoB;QACpB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QAC3D,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;QAE7B,kCAAkC;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,gBAAgB,IAAI,CAAC,CAAC;QACpE,IAAI,OAAO,CAAC,SAAS,GAAG,EAAE,IAAI,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,wCAAwC,QAAQ,yBAAyB,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,gBAAmC;QACrD,MAAM,cAAc,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/D,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAE7C,gDAAgD;QAChD,MAAM,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YACpD,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACvD,OAAO,YAAY,KAAK,MAAM,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAE7C,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,QAAQ,IAAI,gBAAgB,CAAC;QAEzE,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,gBAAgB;gBACnB,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;YAEzF,KAAK,mBAAmB;gBACtB,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;oBAClC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,IAAI,CAAC,CAAC;oBAClE,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,IAAI,CAAC,CAAC;oBAClE,OAAO,YAAY,GAAG,YAAY,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;YAEhB,KAAK,aAAa;gBAChB,oCAAoC;gBACpC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,KAAK,GAAG,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC;gBAChD,OAAO,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;YAEvC,KAAK,QAAQ;gBACX,OAAO,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC;YAEnF;gBACE,OAAO,cAAc,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAElD,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACxC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAClC,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAE3B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACrD,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,yBAAyB,CACvC,YAAkD,EAAE;IAEpD,OAAO;QACL,GAAG,cAAc,CAAC,aAAa;QAC/B,GAAG,SAAS;QACZ,cAAc,EAAE;YACd,uBAAuB,EAAE,CAAC;YAC1B,WAAW,EAAE,MAAM,EAAE,YAAY;YACjC,iBAAiB,EAAE,KAAK,EAAE,aAAa;YACvC,GAAG,SAAS,CAAC,cAAc;SAC5B;QACD,kBAAkB,EAAE;YAClB,kBAAkB,EAAE,GAAG,EAAE,SAAS;YAClC,YAAY,EAAE,GAAG,EAAE,MAAM;YACzB,UAAU,EAAE,KAAK,EAAE,WAAW;YAC9B,GAAG,SAAS,CAAC,kBAAkB;SAChC;QACD,aAAa,EAAE;YACb,QAAQ,EAAE,gBAAgB;YAC1B,GAAG,SAAS,CAAC,aAAa;SAC3B;QACD,cAAc,EAAE;YACd,gBAAgB,EAAE,CAAC;YACnB,eAAe,EAAE,KAAK,EAAE,aAAa;YACrC,gBAAgB,EAAE,CAAC;YACnB,GAAG,SAAS,CAAC,cAAc;SAC5B;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CACjC,SAA+C,EAAE;IAEjD,MAAM,UAAU,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC;IACrD,OAAO,IAAI,aAAa,CAAC,UAAU,CAAC,CAAC;AACvC,CAAC"}