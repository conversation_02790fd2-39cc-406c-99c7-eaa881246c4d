{"version": 3, "file": "server-manager.d.ts", "sourceRoot": "", "sources": ["../../src/config/server-manager.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,KAAK,EAAE,mBAAmB,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAEvE,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAE5D,OAAO,KAAK,EAAE,gBAAgB,EAA0B,MAAM,uBAAuB,CAAC;AAEtF;;GAEG;AACH,MAAM,WAAW,2BAA4B,SAAQ,mBAAmB;IACtE,+BAA+B;IAC/B,cAAc,CAAC,EAAE;QACf,qCAAqC;QACrC,uBAAuB,EAAE,MAAM,CAAC;QAChC,8CAA8C;QAC9C,WAAW,EAAE,MAAM,CAAC;QACpB,qDAAqD;QACrD,iBAAiB,EAAE,MAAM,CAAC;KAC3B,CAAC;IAEF,mCAAmC;IACnC,kBAAkB,CAAC,EAAE;QACnB,4CAA4C;QAC5C,kBAAkB,EAAE,MAAM,CAAC;QAC3B,+CAA+C;QAC/C,YAAY,EAAE,MAAM,CAAC;QACrB,kDAAkD;QAClD,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IAEF,mCAAmC;IACnC,aAAa,CAAC,EAAE;QACd,oCAAoC;QACpC,QAAQ,EAAE,aAAa,GAAG,mBAAmB,GAAG,gBAAgB,GAAG,QAAQ,CAAC;QAC5E,0CAA0C;QAC1C,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAClC,CAAC;IAEF,oCAAoC;IACpC,cAAc,CAAC,EAAE;QACf,+CAA+C;QAC/C,gBAAgB,EAAE,MAAM,CAAC;QACzB,uCAAuC;QACvC,eAAe,EAAE,MAAM,CAAC;QACxB,oCAAoC;QACpC,gBAAgB,EAAE,MAAM,CAAC;KAC1B,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,aAAa;IAC5B,QAAQ,EAAE,MAAM,CAAC;IACjB,eAAe,EAAE,MAAM,CAAC;IACxB,mBAAmB,EAAE,MAAM,CAAC;IAC5B,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;IACjB,eAAe,EAAE,IAAI,CAAC;IACtB,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,WAAW,GAAG,cAAc,CAAC;CAC/D;AAED;;GAEG;AACH,qBAAa,aAAa;IACxB,OAAO,CAAC,MAAM,CAA8B;IAC5C,OAAO,CAAC,OAAO,CAAyC;IACxD,OAAO,CAAC,eAAe,CAAiC;IACxD,OAAO,CAAC,aAAa,CAA2D;IAChF,OAAO,CAAC,mBAAmB,CAA+B;IAC1D,OAAO,CAAC,aAAa,CAAoC;IACzD,OAAO,CAAC,aAAa,CAAiC;gBAE1C,MAAM,EAAE,2BAA2B;IA6B/C;;OAEG;IACH,iBAAiB,IAAI;QACnB,gBAAgB,EAAE,OAAO,CAAC;QAC1B,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAC9B,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAC9B,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;KACpB;IAoBD;;OAEG;IACG,UAAU,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,aAAa,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC;IAyC7F;;OAEG;YACW,kCAAkC;IA2ChD;;OAEG;IACH,OAAO,CAAC,2BAA2B;IA2BnC;;OAEG;IACH,OAAO,CAAC,iCAAiC;IAqCzC;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAY7B;;OAEG;YACW,mBAAmB;IAmCjC;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAQ3B;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAkBzB;;OAEG;IACH,mBAAmB,CAAC,gBAAgB,EAAE,eAAe,EAAE,GAAG,eAAe,GAAG,IAAI;IAuChF;;OAEG;IACH,gBAAgB,IAAI,aAAa,EAAE;IAInC;;OAEG;IACH,uBAAuB,IAAI,gBAAgB,EAAE;IAO7C;;OAEG;IACH,gBAAgB;;;;;;;;IAsBhB;;OAEG;IACG,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAO1E;;OAEG;IACG,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAOzD;;OAEG;IACH,wBAAwB,IAAI,OAAO;IAInC;;OAEG;IACH,SAAS,IAAI,2BAA2B;IAIxC;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAqBhC;AAED;;GAEG;AACH,wBAAgB,yBAAyB,CACvC,SAAS,GAAE,OAAO,CAAC,2BAA2B,CAAM,GACnD,2BAA2B,CA2B7B;AAED;;GAEG;AACH,wBAAgB,mBAAmB,CACjC,MAAM,GAAE,OAAO,CAAC,2BAA2B,CAAM,GAChD,aAAa,CAGf"}