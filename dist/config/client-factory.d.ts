/**
 * MCP Client factory for creating and managing multiple MCP server connections
 */
import { MC<PERSON>lient } from 'mcp-use';
import type { MCPServerConfig, MCPMultiAgentConfig } from './types.js';
/**
 * Factory for creating MCP clients with proper configuration
 */
export declare class MCPClientFactory {
    private config;
    private client;
    constructor(config: MCPMultiAgentConfig);
    /**
     * Create a single MCP client configured with all enabled servers
     */
    createClient(): MCPClient;
    /**
     * Convert our server config format to mcp-use format
     */
    private convertServerConfig;
    /**
     * Create sessions for all enabled servers
     */
    createAllSessions(autoInitialize?: boolean): Promise<Record<string, any>>;
    /**
     * Create a session for a specific server
     */
    createSession(serverId: string, autoInitialize?: boolean): Promise<any>;
    /**
     * Get the underlying MCPClient instance
     */
    getClient(): MCPClient | null;
    /**
     * Get server configuration by ID
     */
    getServerConfig(serverId: string): MCPServerConfig | undefined;
    /**
     * Get all enabled server configurations
     */
    getEnabledServers(): MCPServerConfig[];
    /**
     * Get all server names configured in the client
     */
    getServerNames(): string[];
    /**
     * Close all sessions
     */
    closeAll(): Promise<void>;
    /**
     * Close a specific session
     */
    closeSession(serverId: string): Promise<void>;
    /**
     * Check if a server session is active
     */
    isConnected(serverId: string): boolean;
    /**
     * Get connection status for all servers
     */
    getConnectionStatus(): Record<string, boolean>;
    /**
     * Get all active sessions
     */
    getAllActiveSessions(): Record<string, any>;
}
//# sourceMappingURL=client-factory.d.ts.map