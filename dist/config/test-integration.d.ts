/**
 * Integration test for environment configuration with existing systems
 * Run with: npm run dev:test-integration
 */
/**
 * Test integration between environment config and main config loader
 */
declare function testConfigIntegration(): Promise<void>;
/**
 * Test environment configuration with custom overrides
 */
declare function testCustomOverrides(): Promise<void>;
/**
 * Test error handling and validation
 */
declare function testErrorHandling(): Promise<void>;
/**
 * Test environment detection
 */
declare function testEnvironmentDetection(): Promise<void>;
export { testConfigIntegration, testCustomOverrides, testErrorHandling, testEnvironmentDetection };
//# sourceMappingURL=test-integration.d.ts.map