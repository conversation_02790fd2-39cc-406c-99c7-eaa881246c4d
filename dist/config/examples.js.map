{"version": 3, "file": "examples.js", "sourceRoot": "", "sources": ["../../src/config/examples.ts"], "names": [], "mappings": "AAAA;;GAEG;AAIH;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAoC;IAC9D,qBAAqB;IACrB,UAAU,EAAE;QACV,EAAE,EAAE,YAAY;QAChB,IAAI,EAAE,oBAAoB;QAC1B,WAAW,EAAE,0EAA0E;QACvF,cAAc,EAAE,OAAO;QACvB,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,CAAC,yCAAyC,EAAE,MAAM,CAAC;QACzD,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,CAAC;QACX,IAAI,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,CAAC;QACnC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,IAAI;YACb,iBAAiB,EAAE,CAAC;SACrB;KACF;IAED,qBAAqB;IACrB,OAAO,EAAE;QACP,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,oBAAoB;QAC1B,WAAW,EAAE,iEAAiE;QAC9E,cAAc,EAAE,OAAO;QACvB,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,CAAC,wCAAwC,CAAC;QAChD,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,CAAC;QACX,IAAI,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,CAAC;QAClD,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,IAAI;SACd;KACF;IAED,yBAAyB;IACzB,MAAM,EAAE;QACN,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,wBAAwB;QAC9B,WAAW,EAAE,6DAA6D;QAC1E,cAAc,EAAE,OAAO;QACvB,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,CAAC,qCAAqC,EAAE,oBAAoB,CAAC;QACnE,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,CAAC;QACX,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC;QAC9C,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,IAAI;SACd;KACF;IAED,aAAa;IACb,GAAG,EAAE;QACH,EAAE,EAAE,KAAK;QACT,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,wDAAwD;QACrE,cAAc,EAAE,OAAO;QACvB,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,CAAC,kCAAkC,EAAE,cAAc,EAAE,GAAG,CAAC;QAC/D,OAAO,EAAE,KAAK,EAAE,mCAAmC;QACnD,QAAQ,EAAE,CAAC;QACX,IAAI,EAAE,CAAC,KAAK,EAAE,iBAAiB,EAAE,YAAY,CAAC;QAC9C,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,IAAI;SACd;KACF;IAED,gBAAgB;IAChB,MAAM,EAAE;QACN,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,kDAAkD;QAC/D,cAAc,EAAE,OAAO;QACvB,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,CAAC,qCAAqC,CAAC;QAC7C,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,CAAC;QACX,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC;QACxC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,IAAI;SACd;KACF;IAED,+BAA+B;IAC/B,UAAU,EAAE;QACV,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,oBAAoB;QAC1B,WAAW,EAAE,+BAA+B;QAC5C,cAAc,EAAE,MAAM;QACtB,GAAG,EAAE,2BAA2B;QAChC,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,CAAC;QACX,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC;QAC/B,OAAO,EAAE,KAAK;QACd,OAAO,EAAE;YACP,eAAe,EAAE,wBAAwB;YACzC,cAAc,EAAE,kBAAkB;SACnC;QACD,KAAK,EAAE;YACL,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,IAAI;SACd;KACF;IAED,oBAAoB;IACpB,eAAe,EAAE;QACf,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,yBAAyB;QAC/B,WAAW,EAAE,oCAAoC;QACjD,cAAc,EAAE,WAAW;QAC3B,GAAG,EAAE,yBAAyB;QAC9B,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,CAAC;QACX,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;QACzC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,IAAI;SACd;KACF;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAiC;IAC9D,OAAO,EAAE;QACP,eAAe,CAAC,YAAY,CAAE;QAC9B,eAAe,CAAC,QAAQ,CAAE;QAC1B,eAAe,CAAC,QAAQ,CAAE;KAC3B;IACD,KAAK,EAAE;QACL,QAAQ,EAAE,EAAE;QACZ,OAAO,EAAE,MAAM,EAAE,4BAA4B;QAC7C,cAAc,EAAE,IAAI;QACpB,OAAO,EAAE,IAAI;KACd;IACD,aAAa,EAAE;QACb,OAAO,EAAE,IAAI;QACb,oBAAoB,EAAE,CAAC;QACvB,oBAAoB,EAAE,EAAE;QACxB,gBAAgB,EAAE,IAAI;QACtB,mBAAmB,EAAE,KAAK,EAAE,WAAW;QACvC,aAAa,EAAE,IAAI;KACpB;IACD,OAAO,EAAE;QACP,KAAK,EAAE,OAAO;QACd,MAAM,EAAE,MAAM;KACf;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAiC;IAC7D,OAAO,EAAE;QACP,eAAe,CAAC,YAAY,CAAE;QAC9B,eAAe,CAAC,SAAS,CAAE;QAC3B,eAAe,CAAC,QAAQ,CAAE;QAC1B,eAAe,CAAC,QAAQ,CAAE;KAC3B;IACD,KAAK,EAAE;QACL,QAAQ,EAAE,EAAE;QACZ,OAAO,EAAE,KAAK,EAAE,WAAW;QAC3B,cAAc,EAAE,IAAI;QACpB,OAAO,EAAE,KAAK;KACf;IACD,aAAa,EAAE;QACb,OAAO,EAAE,IAAI;QACb,oBAAoB,EAAE,CAAC;QACvB,oBAAoB,EAAE,EAAE;QACxB,gBAAgB,EAAE,IAAI;QACtB,mBAAmB,EAAE,KAAK,EAAE,aAAa;QACzC,aAAa,EAAE,IAAI;KACpB;IACD,OAAO,EAAE;QACP,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,kBAAkB;KACzB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAiC;IAC1D,OAAO,EAAE;QACP,eAAe,CAAC,YAAY,CAAE;QAC9B,eAAe,CAAC,QAAQ,CAAE;KAC3B;IACD,KAAK,EAAE;QACL,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE,KAAK;QACd,cAAc,EAAE,IAAI;KACrB;IACD,aAAa,EAAE;QACb,OAAO,EAAE,IAAI;QACb,oBAAoB,EAAE,CAAC;QACvB,oBAAoB,EAAE,EAAE;KACzB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAiC;IACvD,OAAO,EAAE;QACP;YACE,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,2BAA2B;YACxC,cAAc,EAAE,OAAO;YACvB,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,CAAC,qCAAqC,CAAC;YAC7C,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;YACxB,OAAO,EAAE,KAAK;SACf;KACF;IACD,KAAK,EAAE;QACL,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE,KAAK;QACd,cAAc,EAAE,IAAI;QACpB,OAAO,EAAE,IAAI;KACd;IACD,aAAa,EAAE;QACb,OAAO,EAAE,KAAK,EAAE,8BAA8B;QAC9C,oBAAoB,EAAE,CAAC;QACvB,oBAAoB,EAAE,EAAE;KACzB;IACD,OAAO,EAAE;QACP,KAAK,EAAE,OAAO;QACd,MAAM,EAAE,MAAM;KACf;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAChC,OAA0B,EAC1B,OAKC;IAED,MAAM,UAAU,GAAG,OAAO,EAAE,WAAW,KAAK,YAAY;QACtD,CAAC,CAAC,iBAAiB;QACnB,CAAC,CAAC,OAAO,EAAE,WAAW,KAAK,MAAM;YACjC,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,kBAAkB,CAAC;IAEvB,OAAO;QACL,GAAG,UAAU;QACb,OAAO;QACP,KAAK,EAAE;YACL,GAAG,UAAU,CAAC,KAAK;YACnB,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,UAAU,CAAC,KAAK,EAAE,QAAQ,IAAI,EAAE;SAChE;QACD,aAAa,EAAE;YACb,GAAG,UAAU,CAAC,aAAa;YAC3B,OAAO,EAAE,OAAO,EAAE,mBAAmB,IAAI,UAAU,CAAC,aAAa,EAAE,OAAO,IAAI,IAAI;SACnF;QACD,OAAO,EAAE;YACP,GAAG,UAAU,CAAC,OAAO;YACrB,KAAK,EAAE,OAAO,EAAE,QAAQ,IAAI,UAAU,CAAC,OAAO,EAAE,KAAK,IAAI,MAAM;YAC/D,MAAM,EAAE,UAAU,CAAC,OAAO,EAAE,MAAM,IAAI,MAAM;SAC7C;KACF,CAAC;AACJ,CAAC"}