/**
 * Test script for server manager configuration
 *
 * This script validates the server manager implementation and demonstrates
 * the performance optimizations and resource management features.
 */
/**
 * Test server manager configuration creation
 */
declare function testServerManagerConfig(): Promise<void>;
/**
 * Test server manager initialization and operations
 */
declare function testServerManagerOperations(): Promise<void>;
/**
 * Test performance scenarios
 */
declare function testPerformanceScenarios(): Promise<void>;
/**
 * Main test function
 */
declare function runServerManagerTests(): Promise<void>;
export { runServerManagerTests, testServerManagerConfig, testServerManagerOperations, testPerformanceScenarios, };
//# sourceMappingURL=test-server-manager.d.ts.map