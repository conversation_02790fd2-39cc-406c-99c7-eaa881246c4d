/**
 * Example configurations for MCP multi-server agent
 */
import type { MCPServerConfig, MCPMultiAgentConfig } from './types.js';
/**
 * Example server configurations for common MCP servers
 */
export declare const EXAMPLE_SERVERS: Record<string, MCPServerConfig>;
/**
 * Example configuration for development
 */
export declare const DEVELOPMENT_CONFIG: Partial<MCPMultiAgentConfig>;
/**
 * Example configuration for production
 */
export declare const PRODUCTION_CONFIG: Partial<MCPMultiAgentConfig>;
/**
 * Minimal configuration with just essential servers
 */
export declare const MINIMAL_CONFIG: Partial<MCPMultiAgentConfig>;
/**
 * Configuration for testing with mock servers
 */
export declare const TEST_CONFIG: Partial<MCPMultiAgentConfig>;
/**
 * Helper function to create a custom configuration
 */
export declare function createCustomConfig(servers: MCPServerConfig[], options?: {
    environment?: 'development' | 'production' | 'test';
    enableServerManager?: boolean;
    maxSteps?: number;
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
}): Partial<MCPMultiAgentConfig>;
//# sourceMappingURL=examples.d.ts.map