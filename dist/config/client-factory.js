/**
 * MCP Client factory for creating and managing multiple MCP server connections
 */
import { MC<PERSON>lient } from 'mcp-use';
/**
 * Factory for creating MCP clients with proper configuration
 */
export class MCPClientFactory {
    config;
    client = null;
    constructor(config) {
        this.config = config;
    }
    /**
     * Create a single MCP client configured with all enabled servers
     */
    createClient() {
        if (this.client) {
            return this.client;
        }
        // Convert our server configs to mcp-use format
        const mcpConfig = {};
        const enabledServers = this.config.servers.filter(server => server.enabled);
        enabledServers.forEach(server => {
            mcpConfig[server.id] = this.convertServerConfig(server);
        });
        // Create client with all server configurations
        this.client = MCPClient.fromDict(mcpConfig);
        return this.client;
    }
    /**
     * Convert our server config format to mcp-use format
     */
    convertServerConfig(serverConfig) {
        const config = {
            name: serverConfig.name,
            description: serverConfig.description,
        };
        // Configure based on connection type
        switch (serverConfig.connectionType) {
            case 'stdio':
                config['connector'] = {
                    type: 'stdio',
                    command: serverConfig.command,
                    args: serverConfig.args || [],
                    env: serverConfig.env || {},
                };
                break;
            case 'http':
                config['connector'] = {
                    type: 'http',
                    url: serverConfig.url,
                    headers: serverConfig.headers || {},
                };
                break;
            case 'websocket':
                config['connector'] = {
                    type: 'websocket',
                    url: serverConfig.url,
                    headers: serverConfig.headers || {},
                };
                break;
            case 'sse':
                config['connector'] = {
                    type: 'sse',
                    url: serverConfig.url,
                    headers: serverConfig.headers || {},
                };
                break;
            default:
                throw new Error(`Unsupported connection type: ${serverConfig.connectionType}`);
        }
        // Add timeout if specified
        if (serverConfig.timeout) {
            config['connector']['timeout'] = serverConfig.timeout;
        }
        return config;
    }
    /**
     * Create sessions for all enabled servers
     */
    async createAllSessions(autoInitialize = true) {
        const client = this.createClient();
        try {
            const sessions = await client.createAllSessions(autoInitialize);
            console.log(`Successfully initialized ${Object.keys(sessions).length} server sessions`);
            return sessions;
        }
        catch (error) {
            console.error('Failed to create server sessions:', error);
            throw error;
        }
    }
    /**
     * Create a session for a specific server
     */
    async createSession(serverId, autoInitialize = true) {
        const client = this.createClient();
        try {
            const session = await client.createSession(serverId, autoInitialize);
            console.log(`Successfully initialized session for server: ${serverId}`);
            return session;
        }
        catch (error) {
            console.error(`Failed to create session for server ${serverId}:`, error);
            throw error;
        }
    }
    /**
     * Get the underlying MCPClient instance
     */
    getClient() {
        return this.client;
    }
    /**
     * Get server configuration by ID
     */
    getServerConfig(serverId) {
        return this.config.servers.find(server => server.id === serverId);
    }
    /**
     * Get all enabled server configurations
     */
    getEnabledServers() {
        return this.config.servers.filter(server => server.enabled);
    }
    /**
     * Get all server names configured in the client
     */
    getServerNames() {
        const client = this.createClient();
        return client.getServerNames();
    }
    /**
     * Close all sessions
     */
    async closeAll() {
        if (this.client) {
            try {
                await this.client.closeAllSessions();
                console.log('All server sessions closed');
            }
            catch (error) {
                console.error('Error closing sessions:', error);
            }
        }
    }
    /**
     * Close a specific session
     */
    async closeSession(serverId) {
        if (this.client) {
            try {
                await this.client.closeSession(serverId);
                console.log(`Session closed for server: ${serverId}`);
            }
            catch (error) {
                console.error(`Error closing session for ${serverId}:`, error);
            }
        }
    }
    /**
     * Check if a server session is active
     */
    isConnected(serverId) {
        if (!this.client) {
            return false;
        }
        const session = this.client.getSession(serverId);
        return session !== null;
    }
    /**
     * Get connection status for all servers
     */
    getConnectionStatus() {
        const status = {};
        this.config.servers.forEach(server => {
            status[server.id] = this.isConnected(server.id);
        });
        return status;
    }
    /**
     * Get all active sessions
     */
    getAllActiveSessions() {
        if (!this.client) {
            return {};
        }
        return this.client.getAllActiveSessions();
    }
}
//# sourceMappingURL=client-factory.js.map