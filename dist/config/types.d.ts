/**
 * Configuration types for MCP multi-server agent
 */
/**
 * Supported MCP server connection types
 */
export type MCPConnectionType = 'http' | 'websocket' | 'stdio' | 'sse';
/**
 * Configuration for a single MCP server
 */
export interface MCPServerConfig {
    /** Unique identifier for the server */
    id: string;
    /** Human-readable name for the server */
    name: string;
    /** Description of the server's capabilities */
    description?: string;
    /** Connection type for this server */
    connectionType: MCPConnectionType;
    /** Server endpoint URL (for HTTP/WebSocket/SSE) */
    url?: string;
    /** Command to start the server (for stdio) */
    command?: string;
    /** Arguments for the command (for stdio) */
    args?: string[];
    /** Environment variables for the server */
    env?: Record<string, string>;
    /** Connection timeout in milliseconds */
    timeout?: number;
    /** Whether this server is enabled */
    enabled: boolean;
    /** Priority for server selection (higher = more preferred) */
    priority?: number;
    /** Tags for categorizing server capabilities */
    tags?: string[];
    /** Custom headers for HTTP/WebSocket connections */
    headers?: Record<string, string>;
    /** Retry configuration */
    retry?: {
        maxAttempts: number;
        delayMs: number;
        backoffMultiplier?: number;
    };
}
/**
 * Agent configuration options
 */
export interface AgentConfig {
    /** Maximum number of steps the agent can take */
    maxSteps?: number;
    /** Timeout for agent operations in milliseconds */
    timeout?: number;
    /** Whether to auto-initialize the agent */
    autoInitialize?: boolean;
    /** Tools that are not allowed to be used */
    disallowedTools?: string[];
    /** Whether to enable verbose logging */
    verbose?: boolean;
    /** Custom system prompt for the agent */
    systemPrompt?: string;
}
/**
 * Server manager configuration
 */
export interface ServerManagerConfig {
    /** Whether to use the server manager */
    enabled: boolean;
    /** Maximum number of concurrent active servers */
    maxConcurrentServers?: number;
    /** Timeout for server startup in seconds */
    serverStartupTimeout?: number;
    /** Whether to enable server health monitoring */
    healthMonitoring?: boolean;
    /** Interval for health checks in milliseconds */
    healthCheckInterval?: number;
    /** Whether to automatically reconnect failed servers */
    autoReconnect?: boolean;
}
/**
 * OpenAI LLM configuration
 */
export interface LLMConfig {
    /** OpenAI API key */
    apiKey: string;
    /** Model to use */
    model?: string;
    /** Temperature for response generation */
    temperature?: number;
    /** Maximum tokens for responses */
    maxTokens?: number;
    /** Maximum number of retries */
    maxRetries?: number;
    /** Retry delay in milliseconds */
    retryDelay?: number;
    /** Base URL for OpenAI API (for custom endpoints) */
    baseURL?: string;
    /** Organization ID */
    organization?: string;
}
/**
 * Complete configuration for the MCP multi-server agent
 */
export interface MCPMultiAgentConfig {
    /** List of MCP servers to connect to */
    servers: MCPServerConfig[];
    /** Agent configuration */
    agent: AgentConfig;
    /** Server manager configuration */
    serverManager: ServerManagerConfig;
    /** LLM configuration */
    llm: LLMConfig;
    /** Global environment variables */
    env?: Record<string, string>;
    /** Logging configuration */
    logging?: {
        level: 'debug' | 'info' | 'warn' | 'error';
        format: 'json' | 'text';
        file?: string;
    };
}
/**
 * Environment variable names used by the application
 */
export declare const ENV_VARS: {
    readonly OPENAI_API_KEY: "OPENAI_API_KEY";
    readonly OPENAI_BASE_URL: "OPENAI_BASE_URL";
    readonly OPENAI_ORGANIZATION: "OPENAI_ORGANIZATION";
    readonly LOG_LEVEL: "LOG_LEVEL";
    readonly LOG_FORMAT: "LOG_FORMAT";
    readonly LOG_FILE: "LOG_FILE";
    readonly MAX_CONCURRENT_SERVERS: "MAX_CONCURRENT_SERVERS";
    readonly SERVER_STARTUP_TIMEOUT: "SERVER_STARTUP_TIMEOUT";
    readonly AGENT_TIMEOUT: "AGENT_TIMEOUT";
    readonly AGENT_MAX_STEPS: "AGENT_MAX_STEPS";
    readonly SERVER_HEALTH_CHECK_INTERVAL: "SERVER_HEALTH_CHECK_INTERVAL";
    readonly SERVER_CONNECTION_POOL_SIZE: "SERVER_CONNECTION_POOL_SIZE";
    readonly SERVER_CIRCUIT_BREAKER_THRESHOLD: "SERVER_CIRCUIT_BREAKER_THRESHOLD";
    readonly SERVER_LOAD_BALANCING_STRATEGY: "SERVER_LOAD_BALANCING_STRATEGY";
};
/**
 * Default configuration values
 */
export declare const DEFAULT_CONFIG: {
    readonly agent: {
        readonly maxSteps: 10;
        readonly timeout: 60000;
        readonly autoInitialize: true;
        readonly verbose: false;
    };
    readonly serverManager: {
        readonly enabled: true;
        readonly maxConcurrentServers: 3;
        readonly serverStartupTimeout: 30;
        readonly healthMonitoring: true;
        readonly healthCheckInterval: 30000;
        readonly autoReconnect: true;
    };
    readonly llm: {
        readonly model: "gpt-4o";
        readonly temperature: 0.1;
        readonly maxTokens: 4096;
        readonly maxRetries: 3;
        readonly retryDelay: 2000;
    };
    readonly logging: {
        readonly level: "info";
        readonly format: "text";
    };
};
//# sourceMappingURL=types.d.ts.map