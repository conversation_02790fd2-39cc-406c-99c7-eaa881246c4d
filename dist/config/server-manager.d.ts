/**
 * Server Manager Configuration for MCP Multi-Server Agent
 *
 * This module provides optimized server manager configuration for performance
 * and resource management when working with multiple MCP servers.
 */
import type { ServerManagerConfig, MCPServerConfig } from './types.js';
/**
 * Advanced server manager configuration options
 */
export interface AdvancedServerManagerConfig extends ServerManagerConfig {
    /** Connection pool settings */
    connectionPool?: {
        /** Maximum connections per server */
        maxConnectionsPerServer: number;
        /** Connection idle timeout in milliseconds */
        idleTimeout: number;
        /** Connection keep-alive interval in milliseconds */
        keepAliveInterval: number;
    };
    /** Resource management settings */
    resourceManagement?: {
        /** Maximum memory usage per server in MB */
        maxMemoryPerServer: number;
        /** CPU usage threshold for throttling (0-1) */
        cpuThreshold: number;
        /** Garbage collection interval in milliseconds */
        gcInterval: number;
    };
    /** Load balancing configuration */
    loadBalancing?: {
        /** Strategy for server selection */
        strategy: 'round-robin' | 'least-connections' | 'priority-based' | 'random';
        /** Weight factors for server selection */
        weights?: Record<string, number>;
    };
    /** Circuit breaker configuration */
    circuitBreaker?: {
        /** Failure threshold before opening circuit */
        failureThreshold: number;
        /** Recovery timeout in milliseconds */
        recoveryTimeout: number;
        /** Half-open state test requests */
        halfOpenMaxCalls: number;
    };
}
/**
 * Server performance metrics
 */
export interface ServerMetrics {
    serverId: string;
    connectionCount: number;
    averageResponseTime: number;
    errorRate: number;
    memoryUsage: number;
    cpuUsage: number;
    lastHealthCheck: Date;
    status: 'healthy' | 'degraded' | 'unhealthy' | 'disconnected';
}
/**
 * Server Manager class for optimized multi-server operations
 */
export declare class ServerManager {
    private config;
    private metrics;
    private connectionPools;
    private circuitStates;
    private healthCheckInterval;
    constructor(config: AdvancedServerManagerConfig);
    /**
     * Get optimized MCPAgent configuration
     */
    getMCPAgentConfig(): {
        useServerManager: boolean;
        maxConcurrentServers?: number;
        serverStartupTimeout?: number;
        [key: string]: any;
    };
    /**
     * Initialize server manager with health monitoring
     */
    initialize(servers: MCPServerConfig[]): Promise<void>;
    /**
     * Start health monitoring for all servers
     */
    private startHealthMonitoring;
    /**
     * Perform health checks on all servers
     */
    private performHealthChecks;
    /**
     * Update server metrics
     */
    private updateServerMetrics;
    /**
     * Handle server errors and circuit breaker logic
     */
    private handleServerError;
    /**
     * Get server selection based on load balancing strategy
     */
    selectOptimalServer(availableServers: MCPServerConfig[]): MCPServerConfig | null;
    /**
     * Get current server metrics
     */
    getServerMetrics(): ServerMetrics[];
    /**
     * Get server manager configuration
     */
    getConfig(): AdvancedServerManagerConfig;
    /**
     * Shutdown server manager and cleanup resources
     */
    shutdown(): Promise<void>;
}
/**
 * Create optimized server manager configuration
 */
export declare function createServerManagerConfig(overrides?: Partial<AdvancedServerManagerConfig>): AdvancedServerManagerConfig;
/**
 * Factory function to create a server manager instance
 */
export declare function createServerManager(config?: Partial<AdvancedServerManagerConfig>): ServerManager;
//# sourceMappingURL=server-manager.d.ts.map