/**
 * Environment configuration module for MCP multi-server agent
 * Provides centralized environment variable handling with validation and type safety
 */
/**
 * Environment validation error with helpful context
 */
export declare class EnvironmentError extends Error {
    constructor(message: string, variable?: string, suggestion?: string);
}
/**
 * Environment variable configuration with validation
 */
export interface EnvironmentConfig {
    openai: {
        apiKey: string;
        baseURL?: string | undefined;
        organization?: string | undefined;
        model?: string | undefined;
        temperature?: number | undefined;
        maxTokens?: number | undefined;
        maxRetries?: number | undefined;
        retryDelay?: number | undefined;
    };
    agent: {
        maxSteps: number;
        timeout: number;
    };
    serverManager: {
        maxConcurrentServers: number;
        startupTimeout: number;
    };
    logging: {
        level: 'debug' | 'info' | 'warn' | 'error';
        format: 'text' | 'json';
        file?: string | undefined;
    };
    environment: {
        nodeEnv: 'development' | 'production' | 'test';
        isDevelopment: boolean;
        isProduction: boolean;
        isTest: boolean;
    };
}
/**
 * Load and validate environment configuration
 */
export declare function loadEnvironmentConfig(): EnvironmentConfig;
export declare function getEnvironmentConfig(): EnvironmentConfig;
/**
 * Reset cached configuration (useful for testing)
 */
export declare function resetEnvironmentConfig(): void;
/**
 * Validate environment configuration without throwing
 */
export declare function validateEnvironmentConfig(): {
    valid: boolean;
    errors: string[];
};
//# sourceMappingURL=env.d.ts.map