/**
 * Integration test for server manager with MultiServerAgent
 *
 * This test validates that the server manager configuration is properly
 * integrated with the MultiServerAgent and provides the expected optimizations.
 */
/**
 * Test server manager integration with MultiServerAgent
 */
declare function testServerManagerIntegration(): Promise<void>;
/**
 * Test different server manager configurations
 */
declare function testServerManagerConfigurations(): Promise<void>;
/**
 * Main test runner
 */
declare function runIntegrationTests(): Promise<void>;
export { runIntegrationTests, testServerManagerIntegration, testServerManagerConfigurations, };
//# sourceMappingURL=test-server-manager-integration.d.ts.map