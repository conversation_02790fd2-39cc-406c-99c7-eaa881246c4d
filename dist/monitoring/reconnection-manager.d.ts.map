{"version": 3, "file": "reconnection-manager.d.ts", "sourceRoot": "", "sources": ["../../src/monitoring/reconnection-manager.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACzD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AACnE,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,oBAAoB,CAAC;AAEjE;;GAEG;AACH,MAAM,WAAW,mBAAmB;IAClC,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAChB,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,KAAK,CAAC;IACd,aAAa,CAAC,EAAE,IAAI,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,QAAQ,EAAE,MAAM,CAAC;IACjB,cAAc,EAAE,OAAO,CAAC;IACxB,aAAa,EAAE,MAAM,CAAC;IACtB,WAAW,CAAC,EAAE,mBAAmB,GAAG,SAAS,CAAC;IAC9C,aAAa,CAAC,EAAE,IAAI,GAAG,SAAS,CAAC;IACjC,YAAY,EAAE,MAAM,CAAC;IACrB,kBAAkB,EAAE,OAAO,CAAC;CAC7B;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,sBAAsB,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;IACnD,sBAAsB,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;IACnD,qBAAqB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;IACjF,wBAAwB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,KAAK,IAAI,CAAC;IAC5E,eAAe,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;CAC5D;AAED;;GAEG;AACH,qBAAa,mBAAmB;IAC9B,OAAO,CAAC,aAAa,CAAmB;IACxC,OAAO,CAAC,MAAM,CAAyB;IACvC,OAAO,CAAC,aAAa,CAA2C;IAEhE,OAAO,CAAC,kBAAkB,CAA8C;IACxE,OAAO,CAAC,oBAAoB,CAA0C;IACtE,OAAO,CAAC,cAAc,CAAwD;IAC9E,OAAO,CAAC,cAAc,CAAS;gBAEnB,aAAa,EAAE,gBAAgB,EAAE,MAAM,EAAE,sBAAsB;IAK3E;;OAEG;IACG,UAAU,CAAC,OAAO,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAmB3D;;OAEG;IACG,oBAAoB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAyC3D;;OAEG;IACG,mBAAmB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAyE1D;;OAEG;IACG,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAqBzD;;OAEG;IACH,kBAAkB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAgB1C;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAa7B;;OAEG;IACH,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAgB/C;;OAEG;IACH,qBAAqB,CAAC,QAAQ,EAAE,MAAM,GAAG,kBAAkB,GAAG,IAAI;IAIlE;;OAEG;IACH,wBAAwB,IAAI,kBAAkB,EAAE;IAIhD;;OAEG;IACH,sBAAsB,IAAI,OAAO;IAIjC;;OAEG;IACH,gBAAgB,IAAI,MAAM,EAAE;IAM5B;;OAEG;IACH,EAAE,CAAC,CAAC,SAAS,MAAM,kBAAkB,EACnC,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,kBAAkB,CAAC,CAAC,CAAC,GAC9B,IAAI;IAOP;;OAEG;IACH,GAAG,CAAC,CAAC,SAAS,MAAM,kBAAkB,EACpC,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,kBAAkB,CAAC,CAAC,CAAC,GAC9B,IAAI;IAUP;;OAEG;IACH,OAAO,CAAC,IAAI;IAgBZ;;OAEG;IACH,aAAa,IAAI;QACf,YAAY,EAAE,MAAM,CAAC;QACrB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,aAAa,EAAE,MAAM,CAAC;QACtB,aAAa,EAAE,MAAM,CAAC;QACtB,uBAAuB,EAAE,MAAM,CAAC;KACjC;IAYD;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAiBhC"}