{"version": 3, "file": "test-health-monitoring.js", "sourceRoot": "", "sources": ["../../src/monitoring/test-health-monitoring.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AACnD,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAIhD;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAEtD,IAAI,CAAC;QACH,qBAAqB;QACrB,MAAM,MAAM,GAAG,UAAU,EAAE,CAAC;QAE5B,uBAAuB;QACvB,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAEvD,8CAA8C;QAC9C,MAAM,WAAW,GAAwB;YACvC,GAAG,MAAM;YACT,aAAa,EAAE;gBACb,GAAG,MAAM,CAAC,aAAa;gBACvB,gBAAgB,EAAE,IAAI;gBACtB,mBAAmB,EAAE,KAAK,EAAE,yBAAyB;gBACrD,aAAa,EAAE,IAAI;aACpB;SACF,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,gBAAgB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;QAEzB,sCAAsC;QACtC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAEvE,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,0BAA0B,YAAY,CAAC,MAAM,oBAAoB,CAAC,CAAC;QAC/E,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,mBAAmB,YAAY,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,+CAA+C;QAC/C,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACrD,MAAM,YAAY,GAAG,KAAK,CAAC,wBAAwB,EAAE,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,6BAA6B,YAAY,EAAE,CAAC,CAAC;QAEzD,iCAAiC;QACjC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,MAAM,iBAAiB,GAAG,MAAM,KAAK,CAAC,eAAe,EAAE,CAAC;QACxD,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,iBAAiB,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,aAAa,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEvG,kDAAkD;QAClD,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;YAC/C,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAC;YAE5D,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAC5D,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO,CAAC,GAAG,CAAC,wBAAwB,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,mBAAmB,SAAS,CAAC,CAAC;YACzG,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,mBAAmB,OAAO,CAAC,MAAM,UAAU,CAAC,CAAC;QACzD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,MAAM,CAAC,mBAAmB,SAAS,CAAC,CAAC;QAC3I,CAAC,CAAC,CAAC;QAEH,2CAA2C;QAC3C,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;QAChF,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAEzD,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAE/E,8DAA8D;QAC9D,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,MAAM,aAAa,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;QAC9C,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,cAAc,IAAI,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;QAE/G,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,mBAAmB,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAC;YAE5D,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAC9D,OAAO,CAAC,GAAG,CAAC,wBAAwB,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QAChF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;QACzE,CAAC;QAED,+BAA+B;QAC/B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAE3E,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QAEnE,UAAU;QACV,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACnC,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;IAEzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,gCAAgC;IACpD,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;IAEvE,IAAI,CAAC;QACH,wDAAwD;QACxD,MAAM,MAAM,GAAG,UAAU,EAAE,CAAC;QAE5B,uBAAuB;QACvB,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAEvD,6DAA6D;QAC7D,MAAM,2BAA2B,GAAgC;YAC/D,GAAG,MAAM,CAAC,aAAa;YACvB,gBAAgB,EAAE,IAAI;YACtB,mBAAmB,EAAE,IAAI,EAAE,YAAY;YACvC,aAAa,EAAE,IAAI;YACnB,cAAc,EAAE;gBACd,gBAAgB,EAAE,CAAC,EAAE,4BAA4B;gBACjD,eAAe,EAAE,KAAK,EAAE,aAAa;gBACrC,gBAAgB,EAAE,CAAC;aACpB;SACF,CAAC;QAEF,MAAM,WAAW,GAAwB;YACvC,GAAG,MAAM;YACT,aAAa,EAAE,2BAA2B;SAC3C,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,gBAAgB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;QAEzB,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACjC,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAEpD,wCAAwC;QACxC,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAEhE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,kBAAkB,GAAG,KAAK,CAAC,CAAC,aAAa;QAE/C,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,kBAAkB,EAAE,CAAC;YACnD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAEjF,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAC/C,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;YAE7C,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YACrF,OAAO,CAAC,GAAG,CAAC,YAAY,aAAa,CAAC,aAAa,KAAK,aAAa,CAAC,cAAc,IAAI,aAAa,CAAC,YAAY,WAAW,CAAC,CAAC;YAE/H,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC7B,MAAM,QAAQ,GAAG,MAAM,CAAC,mBAAmB,CAAC;gBAC5C,MAAM,cAAc,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvE,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,QAAQ,KAAK,MAAM,KAAK,QAAQ,aAAa,cAAc,EAAE,CAAC,CAAC;YAC3F,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QAEnE,UAAU;QACV,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;IAEzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QACvE,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,2BAA2B;IAC/C,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAE3D,IAAI,CAAC;QACH,MAAM,oBAAoB,EAAE,CAAC;QAE7B,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QAE1C,MAAM,gCAAgC,EAAE,CAAC;QAEzC,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;IAE1E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,gBAAgB;AAChB,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAEjC,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,OAAO;YACV,oBAAoB,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM;QACR,KAAK,UAAU;YACb,gCAAgC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACxD,MAAM;QACR,KAAK,KAAK,CAAC;QACX;YACE,2BAA2B,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM;IACV,CAAC;AACH,CAAC"}