{"version": 3, "file": "server-health.js", "sourceRoot": "", "sources": ["../../src/monitoring/server-health.ts"], "names": [], "mappings": "AAAA;;;;;;;;;GASG;AAIH,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACpD,OAAO,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AAqDhE;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAA2B;IAC3D,mBAAmB,EAAE,KAAK,EAAE,aAAa;IACzC,kBAAkB,EAAE,IAAI,EAAE,YAAY;IACtC,gBAAgB,EAAE,CAAC;IACnB,iBAAiB,EAAE,CAAC;IACpB,aAAa,EAAE,IAAI;IACnB,iBAAiB,EAAE,KAAK,EAAE,aAAa;IACvC,oBAAoB,EAAE,CAAC;IACvB,cAAc,EAAE;QACd,OAAO,EAAE,IAAI;QACb,gBAAgB,EAAE,CAAC;QACnB,eAAe,EAAE,KAAK,EAAE,WAAW;QACnC,gBAAgB,EAAE,CAAC;KACpB;CACF,CAAC;AAgBF;;GAEG;AACH,MAAM,OAAO,mBAAmB;IACtB,MAAM,CAAyB;IAC/B,aAAa,CAAgB;IAC7B,mBAAmB,CAAsB;IAEzC,UAAU,GAAkC,IAAI,GAAG,EAAE,CAAC;IACtD,kBAAkB,GAA0B,IAAI,CAAC;IACjD,cAAc,GAAkD,IAAI,GAAG,EAAE,CAAC;IAC1E,YAAY,GAAG,KAAK,CAAC;IAE7B,YACE,aAA+B,EAC/B,SAA0C,EAAE;QAE5C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,qBAAqB,EAAE,GAAG,MAAM,EAAE,CAAC;QACtD,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACnE,IAAI,CAAC,mBAAmB,GAAG,IAAI,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAE/E,0BAA0B;QAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,OAA0B;QACzC,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAExD,iDAAiD;QACjD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;oBAC7B,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,MAAM,EAAE,cAAc;oBACtB,eAAe,EAAE,IAAI,IAAI,EAAE;oBAC3B,mBAAmB,EAAE,IAAI,IAAI,EAAE;oBAC/B,mBAAmB,EAAE,CAAC;oBACtB,mBAAmB,EAAE,CAAC;oBACtB,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,CAAC;oBAClB,MAAM,EAAE,CAAC;oBACT,oBAAoB,EAAE,KAAK;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,qDAAqD;QACrD,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEnD,OAAO,CAAC,GAAG,CAAC,oCAAoC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,UAAU,CAAC,CAAC;IACnG,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YACxD,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,4CAA4C,IAAI,CAAC,MAAM,CAAC,mBAAmB,KAAK,CAAC,CAAC;QAE9F,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC/C,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAEpC,+BAA+B;QAC/B,IAAI,CAAC,mBAAmB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvC,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAEhD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;QAErD,MAAM,mBAAmB,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;YAC3D,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBACpE,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBACtE,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,KAAc,CAAC,CAAC;gBAC9D,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE,KAAc,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE,YAAiB;QAClE,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,KAAK,SAAS,CAAC;QAEpD,2CAA2C;QAC3C,MAAM,WAAW,GAAqB;YACpC,GAAG,WAAW;YACd,eAAe,EAAE,GAAG;YACpB,mBAAmB,EAAE,IAAI,CAAC,4BAA4B,CACpD,WAAW,CAAC,mBAAmB,EAC/B,YAAY,CAAC,YAAY,IAAI,CAAC,CAC/B;SACF,CAAC;QAEF,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;YACzB,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;YAC/B,WAAW,CAAC,mBAAmB,GAAG,GAAG,CAAC;YACtC,WAAW,CAAC,mBAAmB,GAAG,CAAC,CAAC;YACpC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;YAEjE,qDAAqD;YACrD,IAAI,WAAW,CAAC,oBAAoB,IAAI,WAAW,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;gBAC9E,WAAW,CAAC,oBAAoB,GAAG,KAAK,CAAC;gBACzC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,WAAW,CAAC,mBAAmB,EAAE,CAAC;YAClC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;YACnE,WAAW,CAAC,SAAS,GAAG,YAAY,CAAC,KAAK,EAAE,OAAO,IAAI,eAAe,CAAC;YAEvE,0CAA0C;YAC1C,IAAI,WAAW,CAAC,mBAAmB,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBACpE,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC;gBAEjC,+CAA+C;gBAC/C,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO;oBAClC,WAAW,CAAC,mBAAmB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;oBACnF,WAAW,CAAC,oBAAoB,GAAG,IAAI,CAAC;oBACxC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;gBAC7D,CAAC;gBAED,IAAI,UAAU,EAAE,CAAC;oBACf,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC;YAClC,CAAC;QACH,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,QAAgB,EAAE,KAAY;QACnE,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,WAAW,GAAqB;YACpC,GAAG,WAAW;YACd,MAAM,EAAE,cAAc;YACtB,eAAe,EAAE,IAAI,IAAI,EAAE;YAC3B,mBAAmB,EAAE,WAAW,CAAC,mBAAmB,GAAG,CAAC;YACxD,SAAS,EAAE,KAAK,CAAC,OAAO;SACzB,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAC3C,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QAExD,kCAAkC;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,OAAe,EAAE,OAAe;QACnE,IAAI,OAAO,KAAK,CAAC;YAAE,OAAO,OAAO,CAAC;QAClC,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,6BAA6B;IACzE,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,8BAA8B;QAC9B,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,sBAAsB,EAAE,CAAC,QAAgB,EAAE,EAAE;YACvE,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC3C,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;gBACxB,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;gBAC7B,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,QAAgB,EAAE,KAAY,EAAE,OAAe,EAAE,EAAE;YACrG,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,EAAE,CACA,KAAQ,EACR,QAAmC;QAEnC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,GAAG,CACD,KAAQ,EACR,QAAmC;QAEnC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC1C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACf,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,IAAI,CACV,KAAQ,EACR,GAAG,IAA2C;QAE9C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,IAAI,CAAC;oBACF,QAAgB,CAAC,GAAG,IAAI,CAAC,CAAC;gBAC7B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAAgB;QAC9B,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,gBAAgB;QAQd,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC;QACtC,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QAC5E,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;QAChF,MAAM,mBAAmB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,cAAc,CAAC,CAAC,MAAM,CAAC;QAEtF,MAAM,mBAAmB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC,GAAG,YAAY,IAAI,CAAC,CAAC;QAE7G,IAAI,aAAmD,CAAC;QACxD,IAAI,gBAAgB,GAAG,CAAC,IAAI,mBAAmB,GAAG,YAAY,GAAG,CAAC,EAAE,CAAC;YACnE,aAAa,GAAG,WAAW,CAAC;QAC9B,CAAC;aAAM,IAAI,cAAc,GAAG,YAAY,EAAE,CAAC;YACzC,aAAa,GAAG,UAAU,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,SAAS,CAAC;QAC5B,CAAC;QAED,OAAO;YACL,YAAY;YACZ,cAAc;YACd,gBAAgB;YAChB,mBAAmB;YACnB,mBAAmB;YACnB,aAAa;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACrC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACpE,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7E,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,KAAc,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,QAAgB;QACpC,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAEzD,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;QAE1C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAE5B,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC5D,CAAC;CACF"}