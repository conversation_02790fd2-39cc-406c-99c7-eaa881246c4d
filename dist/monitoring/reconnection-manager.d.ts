/**
 * Reconnection Manager for MCP Servers
 *
 * This module handles automatic reconnection logic for failed MCP servers,
 * including exponential backoff, retry limits, and connection recovery.
 */
import type { MCPServerConfig } from '@/config/types.js';
import type { MCPClientFactory } from '@/config/client-factory.js';
import type { HealthMonitoringConfig } from './server-health.js';
/**
 * Reconnection attempt information
 */
export interface ReconnectionAttempt {
    serverId: string;
    attempt: number;
    timestamp: Date;
    success: boolean;
    error?: Error;
    nextAttemptAt?: Date;
}
/**
 * Reconnection status for a server
 */
export interface ReconnectionStatus {
    serverId: string;
    isReconnecting: boolean;
    totalAttempts: number;
    lastAttempt?: ReconnectionAttempt | undefined;
    nextAttemptAt?: Date | undefined;
    backoffDelay: number;
    maxAttemptsReached: boolean;
}
/**
 * Reconnection events
 */
export interface ReconnectionEvents {
    'reconnection-started': (serverId: string) => void;
    'reconnection-success': (serverId: string) => void;
    'reconnection-failed': (serverId: string, error: Error, attempt: number) => void;
    'reconnection-abandoned': (serverId: string, totalAttempts: number) => void;
    'backoff-delay': (serverId: string, delay: number) => void;
}
/**
 * Reconnection Manager class
 */
export declare class ReconnectionManager {
    private clientFactory;
    private config;
    private serverConfigs;
    private reconnectionStatus;
    private reconnectionTimeouts;
    private eventListeners;
    private isShuttingDown;
    constructor(clientFactory: MCPClientFactory, config: HealthMonitoringConfig);
    /**
     * Initialize reconnection manager
     */
    initialize(servers: MCPServerConfig[]): Promise<void>;
    /**
     * Schedule reconnection for a server
     */
    scheduleReconnection(serverId: string): Promise<void>;
    /**
     * Attempt to reconnect to a server
     */
    attemptReconnection(serverId: string): Promise<void>;
    /**
     * Manually trigger reconnection for a server
     */
    reconnectServer(serverId: string): Promise<boolean>;
    /**
     * Cancel scheduled reconnection for a server
     */
    cancelReconnection(serverId: string): void;
    /**
     * Calculate exponential backoff delay
     */
    private calculateBackoffDelay;
    /**
     * Reset reconnection status for a server
     */
    resetReconnectionStatus(serverId: string): void;
    /**
     * Get reconnection status for a server
     */
    getReconnectionStatus(serverId: string): ReconnectionStatus | null;
    /**
     * Get reconnection status for all servers
     */
    getAllReconnectionStatus(): ReconnectionStatus[];
    /**
     * Check if any servers are currently reconnecting
     */
    hasActiveReconnections(): boolean;
    /**
     * Get servers that have reached max attempts
     */
    getFailedServers(): string[];
    /**
     * Add event listener
     */
    on<K extends keyof ReconnectionEvents>(event: K, listener: ReconnectionEvents[K]): void;
    /**
     * Remove event listener
     */
    off<K extends keyof ReconnectionEvents>(event: K, listener: ReconnectionEvents[K]): void;
    /**
     * Emit event
     */
    private emit;
    /**
     * Get reconnection statistics
     */
    getStatistics(): {
        totalServers: number;
        reconnectingServers: number;
        failedServers: number;
        totalAttempts: number;
        successfulReconnections: number;
    };
    /**
     * Shutdown reconnection manager
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=reconnection-manager.d.ts.map