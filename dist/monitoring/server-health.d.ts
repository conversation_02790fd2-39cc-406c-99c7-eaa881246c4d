/**
 * Server Health Monitoring System for MCP Multi-Server Agent
 *
 * This module provides comprehensive health monitoring for MCP servers including:
 * - Real-time health checks
 * - Connection status monitoring
 * - Automatic reconnection logic
 * - Health metrics collection
 * - Circuit breaker functionality
 */
import type { MCPServerConfig } from '@/config/types.js';
import type { MCPClientFactory } from '@/config/client-factory.js';
/**
 * Health status for a server
 */
export type ServerHealthStatus = 'healthy' | 'degraded' | 'unhealthy' | 'disconnected' | 'reconnecting';
/**
 * Detailed health information for a server
 */
export interface ServerHealthInfo {
    serverId: string;
    status: ServerHealthStatus;
    lastHealthCheck: Date;
    lastSuccessfulCheck: Date;
    consecutiveFailures: number;
    averageResponseTime: number;
    errorRate: number;
    connectionCount: number;
    memoryUsage?: number;
    cpuUsage?: number;
    lastError?: string;
    uptime: number;
    isCircuitBreakerOpen: boolean;
}
/**
 * Health monitoring configuration
 */
export interface HealthMonitoringConfig {
    /** Health check interval in milliseconds */
    healthCheckInterval: number;
    /** Timeout for health checks in milliseconds */
    healthCheckTimeout: number;
    /** Number of consecutive failures before marking as unhealthy */
    failureThreshold: number;
    /** Number of consecutive successes needed to mark as healthy */
    recoveryThreshold: number;
    /** Whether to enable automatic reconnection */
    autoReconnect: boolean;
    /** Reconnection retry interval in milliseconds */
    reconnectInterval: number;
    /** Maximum number of reconnection attempts */
    maxReconnectAttempts: number;
    /** Circuit breaker configuration */
    circuitBreaker: {
        enabled: boolean;
        failureThreshold: number;
        recoveryTimeout: number;
        halfOpenMaxCalls: number;
    };
}
/**
 * Default health monitoring configuration
 */
export declare const DEFAULT_HEALTH_CONFIG: HealthMonitoringConfig;
/**
 * Health monitoring events
 */
export interface HealthMonitoringEvents {
    'server-healthy': (serverId: string, info: ServerHealthInfo) => void;
    'server-unhealthy': (serverId: string, info: ServerHealthInfo) => void;
    'server-disconnected': (serverId: string, info: ServerHealthInfo) => void;
    'server-reconnected': (serverId: string, info: ServerHealthInfo) => void;
    'circuit-breaker-opened': (serverId: string, info: ServerHealthInfo) => void;
    'circuit-breaker-closed': (serverId: string, info: ServerHealthInfo) => void;
    'health-check-failed': (serverId: string, error: Error) => void;
    'reconnection-failed': (serverId: string, error: Error, attempt: number) => void;
}
/**
 * Server Health Monitoring System
 */
export declare class ServerHealthMonitor {
    private config;
    private healthChecker;
    private reconnectionManager;
    private healthInfo;
    private monitoringInterval;
    private eventListeners;
    private isMonitoring;
    constructor(clientFactory: MCPClientFactory, config?: Partial<HealthMonitoringConfig>);
    /**
     * Initialize health monitoring for servers
     */
    initialize(servers: MCPServerConfig[]): Promise<void>;
    /**
     * Start health monitoring
     */
    startMonitoring(): void;
    /**
     * Stop health monitoring
     */
    stopMonitoring(): void;
    /**
     * Perform health checks on all monitored servers
     */
    private performHealthChecks;
    /**
     * Update server health information
     */
    private updateServerHealth;
    /**
     * Handle health check failure
     */
    private handleHealthCheckFailure;
    /**
     * Calculate average response time
     */
    private calculateAverageResponseTime;
    /**
     * Set up event forwarding from health checker and reconnection manager
     */
    private setupEventForwarding;
    /**
     * Add event listener
     */
    on<K extends keyof HealthMonitoringEvents>(event: K, listener: HealthMonitoringEvents[K]): void;
    /**
     * Remove event listener
     */
    off<K extends keyof HealthMonitoringEvents>(event: K, listener: HealthMonitoringEvents[K]): void;
    /**
     * Emit event
     */
    private emit;
    /**
     * Get health information for a specific server
     */
    getServerHealth(serverId: string): ServerHealthInfo | null;
    /**
     * Get health information for all servers
     */
    getAllServerHealth(): ServerHealthInfo[];
    /**
     * Get health summary
     */
    getHealthSummary(): {
        totalServers: number;
        healthyServers: number;
        unhealthyServers: number;
        disconnectedServers: number;
        averageResponseTime: number;
        overallStatus: 'healthy' | 'degraded' | 'unhealthy';
    };
    /**
     * Force health check for a specific server
     */
    forceHealthCheck(serverId: string): Promise<ServerHealthInfo | null>;
    /**
     * Manually trigger reconnection for a server
     */
    reconnectServer(serverId: string): Promise<boolean>;
    /**
     * Get monitoring status
     */
    isMonitoringActive(): boolean;
    /**
     * Get configuration
     */
    getConfig(): HealthMonitoringConfig;
    /**
     * Shutdown health monitoring
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=server-health.d.ts.map