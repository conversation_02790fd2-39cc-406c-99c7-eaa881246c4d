/**
 * Monitoring Module for MCP Multi-Server Agent
 *
 * This module provides comprehensive monitoring capabilities including:
 * - Server health monitoring
 * - Connection status tracking
 * - Automatic reconnection management
 * - Health metrics and reporting
 */
export { ServerHealthMonitor } from './server-health.js';
export { <PERSON>Checker } from './health-checker.js';
export { ReconnectionManager } from './reconnection-manager.js';
export type { ServerHealthStatus, ServerHealthInfo, HealthMonitoringConfig, HealthMonitoringEvents, } from './server-health.js';
export type { HealthCheckResult, HealthCheckOptions, } from './health-checker.js';
export type { ReconnectionAttempt, ReconnectionStatus, ReconnectionEvents, } from './reconnection-manager.js';
export { DEFAULT_HEALTH_CONFIG } from './server-health.js';
/**
 * Factory function to create a configured health monitoring system
 */
import type { MCPClientFactory } from '@/config/client-factory.js';
import type { MCPServerConfig } from '@/config/types.js';
import { ServerHealthMonitor } from './server-health.js';
import type { HealthMonitoringConfig } from './server-health.js';
export declare function createHealthMonitor(clientFactory: MCPClientFactory, servers: MCPServerConfig[], config?: Partial<HealthMonitoringConfig>): Promise<ServerHealthMonitor>;
/**
 * Utility function to create health monitoring with automatic startup
 */
export declare function createAndStartHealthMonitor(clientFactory: MCPClientFactory, servers: MCPServerConfig[], config?: Partial<HealthMonitoringConfig>): Promise<ServerHealthMonitor>;
//# sourceMappingURL=index.d.ts.map