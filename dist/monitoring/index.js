/**
 * Monitoring Module for MCP Multi-Server Agent
 *
 * This module provides comprehensive monitoring capabilities including:
 * - Server health monitoring
 * - Connection status tracking
 * - Automatic reconnection management
 * - Health metrics and reporting
 */
// Core monitoring components
export { ServerHealthMonitor } from './server-health.js';
export { <PERSON><PERSON><PERSON><PERSON> } from './health-checker.js';
export { ReconnectionManager } from './reconnection-manager.js';
// Default configuration
export { DEFAULT_HEALTH_CONFIG } from './server-health.js';
import { ServerHealthMonitor, DEFAULT_HEALTH_CONFIG } from './server-health.js';
export async function createHealthMonitor(clientFactory, servers, config = {}) {
    const fullConfig = { ...DEFAULT_HEALTH_CONFIG, ...config };
    const monitor = new ServerHealthMonitor(clientFactory, fullConfig);
    await monitor.initialize(servers);
    return monitor;
}
/**
 * Utility function to create health monitoring with automatic startup
 */
export async function createAndStartHealthMonitor(clientFactory, servers, config = {}) {
    const monitor = await createHealthMonitor(clientFactory, servers, config);
    monitor.startMonitoring();
    return monitor;
}
//# sourceMappingURL=index.js.map