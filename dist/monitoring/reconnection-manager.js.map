{"version": 3, "file": "reconnection-manager.js", "sourceRoot": "", "sources": ["../../src/monitoring/reconnection-manager.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AA0CH;;GAEG;AACH,MAAM,OAAO,mBAAmB;IACtB,aAAa,CAAmB;IAChC,MAAM,CAAyB;IAC/B,aAAa,GAAiC,IAAI,GAAG,EAAE,CAAC;IAExD,kBAAkB,GAAoC,IAAI,GAAG,EAAE,CAAC;IAChE,oBAAoB,GAAgC,IAAI,GAAG,EAAE,CAAC;IAC9D,cAAc,GAA8C,IAAI,GAAG,EAAE,CAAC;IACtE,cAAc,GAAG,KAAK,CAAC;IAE/B,YAAY,aAA+B,EAAE,MAA8B;QACzE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,OAA0B;QACzC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAEvD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;gBAC1C,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;oBACrC,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,cAAc,EAAE,KAAK;oBACrB,aAAa,EAAE,CAAC;oBAChB,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB;oBAC3C,kBAAkB,EAAE,KAAK;iBAC1B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,0CAA0C,IAAI,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,CAAC;IAC3F,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,QAAgB;QACzC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,8CAA8C,QAAQ,EAAE,CAAC,CAAC;YACvE,OAAO;QACT,CAAC;QAED,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,2CAA2C,QAAQ,EAAE,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QAED,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,4CAA4C,QAAQ,EAAE,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,kCAAkC,QAAQ,aAAa,MAAM,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;QAEhG,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QAE5C,mDAAmD;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC/D,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;QAC5B,MAAM,CAAC,aAAa,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC;QAEpD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAE5C,oCAAoC;QACpC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;YACpC,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QACxC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;QACT,CAAC;QAED,MAAM,CAAC,aAAa,EAAE,CAAC;QACvB,MAAM,OAAO,GAAwB;YACnC,QAAQ;YACR,OAAO,EAAE,MAAM,CAAC,aAAa;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,iCAAiC,QAAQ,aAAa,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAE1H,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEhD,+CAA+C;YAC/C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,gCAAgC;YAChC,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAEvD,+BAA+B;YAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC7D,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YAED,WAAW;YACX,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YACvB,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC;YAC7B,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC;YAC9B,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,mCAAmC;YAC7D,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,gBAAgB;YACrE,MAAM,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAElC,OAAO,CAAC,GAAG,CAAC,iCAAiC,QAAQ,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QAE9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;YACxB,OAAO,CAAC,KAAK,GAAG,KAAc,CAAC;YAC/B,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC;YAE7B,OAAO,CAAC,KAAK,CAAC,0BAA0B,OAAO,CAAC,OAAO,eAAe,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1F,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE,KAAc,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YAE5E,qCAAqC;YACrC,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;gBAC7D,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBACjC,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC;gBAE9B,OAAO,CAAC,IAAI,CAAC,iCAAiC,IAAI,CAAC,MAAM,CAAC,oBAAoB,iBAAiB,QAAQ,EAAE,CAAC,CAAC;gBAC3G,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,QAAQ,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;YACtE,CAAC;iBAAM,CAAC;gBACN,wBAAwB;gBACxB,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC;gBAC9B,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,QAAgB;QACpC,OAAO,CAAC,GAAG,CAAC,wCAAwC,QAAQ,EAAE,CAAC,CAAC;QAEhE,oCAAoC;QACpC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAElC,eAAe;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC;YACzB,MAAM,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAClC,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC;QAChC,CAAC;QAED,iCAAiC;QACjC,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAEzC,wBAAwB;QACxB,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAgB;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,OAAO,EAAE,CAAC;YACZ,YAAY,CAAC,OAAO,CAAC,CAAC;YACtB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC;YAC9B,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC;QACnC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,iCAAiC,QAAQ,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,aAAqB;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAChD,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,gBAAgB;QAEzC,mDAAmD;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,QAAQ,CAAC,CAAC;QAEzE,6CAA6C;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC;QAE3C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,QAAgB;QACtC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAElC,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC;YACzB,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC;YAC9B,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;YACpD,MAAM,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAClC,MAAM,CAAC,WAAW,GAAG,SAAS,CAAC;YAC/B,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC;QACnC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,QAAgB;QACpC,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;aAChD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,kBAAkB,CAAC;aAC3C,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,EAAE,CACA,KAAQ,EACR,QAA+B;QAE/B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,GAAG,CACD,KAAQ,EACR,QAA+B;QAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC1C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACf,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,IAAI,CACV,KAAQ,EACR,GAAG,IAAuC;QAE1C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,IAAI,CAAC;oBACF,QAAgB,CAAC,GAAG,IAAI,CAAC,CAAC;gBAC7B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC7E,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QAOX,MAAM,SAAS,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAElD,OAAO;YACL,YAAY,EAAE,SAAS,CAAC,MAAM;YAC9B,mBAAmB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,MAAM;YACnE,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,MAAM;YACjE,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC;YACrE,uBAAuB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,MAAM;SAC9E,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAExD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,mCAAmC;QACnC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,EAAE,CAAC;YACxD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;QAED,iBAAiB;QACjB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAE5B,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAC3D,CAAC;CACF"}