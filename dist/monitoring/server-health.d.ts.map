{"version": 3, "file": "server-health.d.ts", "sourceRoot": "", "sources": ["../../src/monitoring/server-health.ts"], "names": [], "mappings": "AAAA;;;;;;;;;GASG;AAEH,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACzD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAInE;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,SAAS,GAAG,UAAU,GAAG,WAAW,GAAG,cAAc,GAAG,cAAc,CAAC;AAExG;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAC/B,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,kBAAkB,CAAC;IAC3B,eAAe,EAAE,IAAI,CAAC;IACtB,mBAAmB,EAAE,IAAI,CAAC;IAC1B,mBAAmB,EAAE,MAAM,CAAC;IAC5B,mBAAmB,EAAE,MAAM,CAAC;IAC5B,SAAS,EAAE,MAAM,CAAC;IAClB,eAAe,EAAE,MAAM,CAAC;IACxB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,oBAAoB,EAAE,OAAO,CAAC;CAC/B;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC,4CAA4C;IAC5C,mBAAmB,EAAE,MAAM,CAAC;IAC5B,gDAAgD;IAChD,kBAAkB,EAAE,MAAM,CAAC;IAC3B,iEAAiE;IACjE,gBAAgB,EAAE,MAAM,CAAC;IACzB,gEAAgE;IAChE,iBAAiB,EAAE,MAAM,CAAC;IAC1B,+CAA+C;IAC/C,aAAa,EAAE,OAAO,CAAC;IACvB,kDAAkD;IAClD,iBAAiB,EAAE,MAAM,CAAC;IAC1B,8CAA8C;IAC9C,oBAAoB,EAAE,MAAM,CAAC;IAC7B,oCAAoC;IACpC,cAAc,EAAE;QACd,OAAO,EAAE,OAAO,CAAC;QACjB,gBAAgB,EAAE,MAAM,CAAC;QACzB,eAAe,EAAE,MAAM,CAAC;QACxB,gBAAgB,EAAE,MAAM,CAAC;KAC1B,CAAC;CACH;AAED;;GAEG;AACH,eAAO,MAAM,qBAAqB,EAAE,sBAcnC,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC,gBAAgB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,KAAK,IAAI,CAAC;IACrE,kBAAkB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,KAAK,IAAI,CAAC;IACvE,qBAAqB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,KAAK,IAAI,CAAC;IAC1E,oBAAoB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,KAAK,IAAI,CAAC;IACzE,wBAAwB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,KAAK,IAAI,CAAC;IAC7E,wBAAwB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,KAAK,IAAI,CAAC;IAC7E,qBAAqB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IAChE,qBAAqB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;CAClF;AAED;;GAEG;AACH,qBAAa,mBAAmB;IAC9B,OAAO,CAAC,MAAM,CAAyB;IACvC,OAAO,CAAC,aAAa,CAAgB;IACrC,OAAO,CAAC,mBAAmB,CAAsB;IAEjD,OAAO,CAAC,UAAU,CAA4C;IAC9D,OAAO,CAAC,kBAAkB,CAA+B;IACzD,OAAO,CAAC,cAAc,CAA4D;IAClF,OAAO,CAAC,YAAY,CAAS;gBAG3B,aAAa,EAAE,gBAAgB,EAC/B,MAAM,GAAE,OAAO,CAAC,sBAAsB,CAAM;IAU9C;;OAEG;IACG,UAAU,CAAC,OAAO,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IA4B3D;;OAEG;IACH,eAAe,IAAI,IAAI;IAmBvB;;OAEG;IACH,cAAc,IAAI,IAAI;IAgBtB;;OAEG;YACW,mBAAmB;IAiBjC;;OAEG;YACW,kBAAkB;IA2DhC;;OAEG;YACW,wBAAwB;IAqBtC;;OAEG;IACH,OAAO,CAAC,4BAA4B;IAKpC;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAgB5B;;OAEG;IACH,EAAE,CAAC,CAAC,SAAS,MAAM,sBAAsB,EACvC,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,sBAAsB,CAAC,CAAC,CAAC,GAClC,IAAI;IAOP;;OAEG;IACH,GAAG,CAAC,CAAC,SAAS,MAAM,sBAAsB,EACxC,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,sBAAsB,CAAC,CAAC,CAAC,GAClC,IAAI;IAUP;;OAEG;IACH,OAAO,CAAC,IAAI;IAgBZ;;OAEG;IACH,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI;IAI1D;;OAEG;IACH,kBAAkB,IAAI,gBAAgB,EAAE;IAIxC;;OAEG;IACH,gBAAgB,IAAI;QAClB,YAAY,EAAE,MAAM,CAAC;QACrB,cAAc,EAAE,MAAM,CAAC;QACvB,gBAAgB,EAAE,MAAM,CAAC;QACzB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,mBAAmB,EAAE,MAAM,CAAC;QAC5B,aAAa,EAAE,SAAS,GAAG,UAAU,GAAG,WAAW,CAAC;KACrD;IA4BD;;OAEG;IACG,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAY1E;;OAEG;IACG,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAIzD;;OAEG;IACH,kBAAkB,IAAI,OAAO;IAI7B;;OAEG;IACH,SAAS,IAAI,sBAAsB;IAInC;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAWhC"}