/**
 * Health Checker for MCP Servers
 *
 * This module provides detailed health checking functionality for MCP servers,
 * including connection testing, response time measurement, and tool availability checks.
 */
import type { MCPServerConfig } from '@/config/types.js';
import type { MCPClientFactory } from '@/config/client-factory.js';
import type { HealthMonitoringConfig } from './server-health.js';
/**
 * Health check result
 */
export interface HealthCheckResult {
    success: boolean;
    responseTime: number;
    error?: Error;
    details: {
        connectionStatus: 'connected' | 'disconnected' | 'error';
        toolsAvailable: number;
        sessionActive: boolean;
        lastActivity?: Date;
        memoryUsage?: number;
        errorCount: number;
    };
}
/**
 * Health check options
 */
export interface HealthCheckOptions {
    timeout?: number;
    includeToolCheck?: boolean;
    includeMemoryCheck?: boolean;
    testToolExecution?: boolean;
}
/**
 * Health Checker class for MCP servers
 */
export declare class HealthChecker {
    private clientFactory;
    private config;
    private serverConfigs;
    constructor(clientFactory: MCPClientFactory, config: HealthMonitoringConfig);
    /**
     * Initialize health checker with server configurations
     */
    initialize(servers: MCPServerConfig[]): Promise<void>;
    /**
     * Perform comprehensive health check on a server
     */
    checkServerHealth(serverId: string, options?: HealthCheckOptions): Promise<HealthCheckResult>;
    /**
     * Perform the actual health check operations
     */
    private performHealthCheck;
    /**
     * Check tool availability for a server
     */
    private checkToolAvailability;
    /**
     * Check memory usage for a server (if available)
     */
    private checkMemoryUsage;
    /**
     * Test tool execution to verify server responsiveness
     */
    private testToolExecution;
    /**
     * Perform a quick connection test
     */
    quickConnectionTest(serverId: string): Promise<boolean>;
    /**
     * Perform a comprehensive health check
     */
    comprehensiveHealthCheck(serverId: string): Promise<HealthCheckResult>;
    /**
     * Check if a server is responsive
     */
    isServerResponsive(serverId: string): Promise<boolean>;
    /**
     * Get server configuration
     */
    getServerConfig(serverId: string): MCPServerConfig | undefined;
    /**
     * Get all monitored server IDs
     */
    getMonitoredServerIds(): string[];
    /**
     * Update server configuration
     */
    updateServerConfig(serverId: string, config: MCPServerConfig): void;
    /**
     * Remove server from monitoring
     */
    removeServer(serverId: string): void;
    /**
     * Get health checker statistics
     */
    getStatistics(): {
        totalServers: number;
        monitoredServers: number;
        averageCheckTime: number;
    };
}
//# sourceMappingURL=health-checker.d.ts.map