{"version": 3, "file": "health-checker.js", "sourceRoot": "", "sources": ["../../src/monitoring/health-checker.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAiCH;;GAEG;AACH,MAAM,OAAO,aAAa;IAChB,aAAa,CAAmB;IAChC,MAAM,CAAyB;IAC/B,aAAa,GAAiC,IAAI,GAAG,EAAE,CAAC;IAEhE,YAAY,aAA+B,EAAE,MAA8B;QACzE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,OAA0B;QACzC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAEjD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACrB,QAAgB,EAChB,UAA8B,EAAE;QAEhC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAElE,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACtE,MAAM,cAAc,GAAG,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;gBACtD,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC,CAAC;YACxE,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,OAAO;gBACL,GAAG,MAAM;gBACT,YAAY;gBACZ,OAAO,EAAE,IAAI;aACd,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,YAAY;gBACZ,KAAK,EAAE,KAAc;gBACrB,OAAO,EAAE;oBACP,gBAAgB,EAAE,OAAO;oBACzB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,KAAK;oBACpB,UAAU,EAAE,CAAC;iBACd;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,QAAgB,EAChB,OAA2B;QAE3B,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,MAAM,OAAO,GAAiC;YAC5C,gBAAgB,EAAE,cAAc;YAChC,cAAc,EAAE,CAAC;YACjB,aAAa,EAAE,KAAK;YACpB,UAAU,EAAE,CAAC;SACd,CAAC;QAEF,mCAAmC;QACnC,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC7D,OAAO,CAAC,gBAAgB,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC;QACtE,OAAO,CAAC,aAAa,GAAG,WAAW,CAAC;QAEpC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,8BAA8B;YAC9B,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACvD,OAAO,CAAC,gBAAgB,GAAG,WAAW,CAAC;gBACvC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;YAC/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC;gBACnC,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,mCAAoC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;QAED,0CAA0C;QAC1C,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,EAAE,CAAC;YACvC,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;gBAC7D,OAAO,CAAC,cAAc,GAAG,SAAS,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,yCAAyC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAC1D,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;YACpC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,oCAAoC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,sCAAsC;QACtC,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,qCAAqC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,OAAO,EAAE,OAAO,EAAE,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAClD,IAAI,CAAC;YACH,wCAAwC;YACxC,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;YACjE,MAAM,OAAO,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEzC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,qDAAqD;YACrD,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC;gBAC5C,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,SAAS,EAAE,CAAC;gBACxC,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,yDAAyD;gBACzD,OAAO,CAAC,CAAC;YACX,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,mCAAoC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC7C,IAAI,CAAC;YACH,uCAAuC;YACvC,uCAAuC;YACvC,uDAAuD;YACvD,4CAA4C;YAC5C,0CAA0C;YAE1C,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;YACjE,MAAM,OAAO,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEzC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,CAAC,CAAC;YACX,CAAC;YAED,kDAAkD;YAClD,kFAAkF;YAClF,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,mBAAmB;YAChE,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,8BAA+B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAC9C,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;YACjE,MAAM,OAAO,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEzC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,uDAAuD;YACvD,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC;gBAC5C,MAAM,OAAO,CAAC,SAAS,EAAE,CAAC;YAC5B,CAAC;iBAAM,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC9C,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,kEAAkE;gBAClE,gCAAgC;gBAChC,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,oBAAoB,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,+BAAgC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;gBACpD,OAAO,EAAE,IAAI,EAAE,yBAAyB;gBACxC,gBAAgB,EAAE,KAAK;gBACvB,kBAAkB,EAAE,KAAK;gBACzB,iBAAiB,EAAE,KAAK;aACzB,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,KAAK,WAAW,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,uCAAuC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,QAAgB;QAC7C,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;YAC5C,gBAAgB,EAAE,IAAI;YACtB,kBAAkB,EAAE,IAAI;YACxB,iBAAiB,EAAE,IAAI;SACxB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;gBACpD,OAAO,EAAE,IAAI;gBACb,gBAAgB,EAAE,IAAI;gBACtB,iBAAiB,EAAE,KAAK;aACzB,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,OAAO;gBACd,MAAM,CAAC,OAAO,CAAC,gBAAgB,KAAK,WAAW;gBAC/C,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAAgB;QAC9B,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAgB,EAAE,MAAuB;QAC1D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAgB;QAC3B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,aAAa;QAKX,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;YACrC,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;YACzC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB;SACjD,CAAC;IACJ,CAAC;CACF"}