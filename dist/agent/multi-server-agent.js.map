{"version": 3, "file": "multi-server-agent.js", "sourceRoot": "", "sources": ["../../src/agent/multi-server-agent.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACnC,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAG/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,EAAE,aAAa,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AAmChF;;GAEG;AACH,MAAM,OAAO,gBAAgB;IACnB,MAAM,CAAsB;IAC5B,aAAa,CAAmB;IAChC,YAAY,CAAe;IAC3B,aAAa,CAAgB;IAC7B,QAAQ,GAAoB,IAAI,CAAC;IACjC,YAAY,GAAsB,IAAI,CAAC;IACvC,WAAW,GAAG,KAAK,CAAC;IAE5B,YACE,MAA2B,EAC3B,YAA0B;QAE1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YAErD,4EAA4E;YAC5E,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAE7E,gDAAgD;YAChD,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;YACpD,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC,MAAM,cAAc,CAAC,CAAC;YAE5F,oEAAoE;YACpE,MAAM,SAAS,GAAQ;gBACrB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK;gBAChC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM;aACrC,CAAC;YAEF,mDAAmD;YACnD,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC9C,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC;YACtD,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5C,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC;YAClD,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;gBAC5B,SAAS,CAAC,aAAa,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACjE,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;gBACjC,SAAS,CAAC,aAAa,GAAG;oBACxB,GAAG,SAAS,CAAC,aAAa;oBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY;iBAC3C,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;YAE9C,8DAA8D;YAC9D,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;YACnE,MAAM,WAAW,GAAQ;gBACvB,GAAG,EAAE,IAAI,CAAC,YAAY;gBACtB,MAAM,EAAE,SAAS;gBACjB,0CAA0C;gBAC1C,GAAG,mBAAmB;aACvB,CAAC;YAEF,mDAAmD;YACnD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC7C,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpD,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC5C,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;YAClD,CAAC;YAED,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC;YAE1C,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,sBAAsB,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;YACnG,OAAO,CAAC,GAAG,CAAC,8BAA8B,mBAAmB,CAAC,oBAAoB,EAAE,CAAC,CAAC;YACtF,OAAO,CAAC,GAAG,CAAC,8BAA8B,mBAAmB,CAAC,oBAAoB,GAAG,CAAC,CAAC;YACvF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5G,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CAAC,KAAa,EAAE,UAA2B,EAAE;QACpD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QAEhE,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,GAAG,CAAC,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,iBAAiB,QAAQ,cAAc,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;YAErG,iCAAiC;YACjC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAExD,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,OAAO,CAAC,GAAG,CAAC,wBAAwB,aAAa,IAAI,CAAC,CAAC;YAEvD,OAAO;gBACL,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,EAAE,6CAA6C;gBAClG,aAAa;gBACb,SAAS,EAAE,EAAE,EAAE,6CAA6C;gBAC5D,QAAQ,EAAE,EAAE;aACb,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,OAAO,CAAC,KAAK,CAAC,wBAAwB,aAAa,KAAK,EAAE,KAAK,CAAC,CAAC;YAEjE,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACvG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACb,KAAa,EACb,UAA2B,EAAE,EAC7B,OAAiC;QAEjC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,gCAAgC,KAAK,GAAG,CAAC,CAAC;YAEtD,sDAAsD;YACtD,6EAA6E;YAC7E,MAAM,QAAQ,GAAkB;gBAC9B;oBACE,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE;wCACqB,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;qBACnF,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE;iBACnC;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,KAAK;iBACf;aACF,CAAC;YAEF,IAAI,YAAY,GAAG,EAAE,CAAC;YAEtB,MAAM,aAAa,GAAQ,EAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC9C,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC;YAC1D,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5C,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC;YACtD,CAAC;YAED,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YAElF,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;gBAC1C,YAAY,IAAI,KAAK,CAAC;gBACtB,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC;YACH,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,OAAO,CAAC,GAAG,CAAC,kCAAkC,aAAa,IAAI,CAAC,CAAC;YAEjE,OAAO;gBACL,QAAQ,EAAE,YAAY;gBACtB,KAAK,EAAE,CAAC,EAAE,kBAAkB;gBAC5B,aAAa;gBACb,SAAS,EAAE,CAAC,eAAe,CAAC,EAAE,+BAA+B;gBAC7D,QAAQ,EAAE,CAAC,mEAAmE,CAAC;aAChF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,OAAO,CAAC,KAAK,CAAC,kCAAkC,aAAa,KAAK,EAAE,KAAK,CAAC,CAAC;YAE3E,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3G,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QAWjB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACvC,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;QAE9D,OAAO;YACL,OAAO,EAAE,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACjC,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,0BAA0B;gBAC7D,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,cAAc,EAAE,MAAM,CAAC,cAAc;aACtC,CAAC,CAAC;YACH,YAAY,EAAE,UAAU,CAAC,MAAM;YAC/B,cAAc,EAAE,cAAc,CAAC,MAAM;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QAInB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;QAC9D,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,MAAM,MAAM,GAA+C,EAAE,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,6BAA6B,cAAc,CAAC,MAAM,aAAa,CAAC,CAAC;QAE7E,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;YACpC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBACxD,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,EAAE,0BAA0B,CAAC,CAAC;YACxD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5E,MAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;gBAC1D,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,EAAE,yBAAyB,YAAY,EAAE,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,aAAa,CAAC,uBAAuB,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACrC,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,QAAgB;QACpC,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC,wBAAwB,EAAE,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAEtD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YACpC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,qCAAqC;IAClE,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB,CAC1C,MAA2B,EAC3B,YAA0B;IAE1B,MAAM,KAAK,GAAG,IAAI,gBAAgB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAEzD,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;QAChC,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}