/**
 * Test suite for MultiServerAgent
 *
 * This file provides comprehensive testing for the agent implementation
 * including initialization, server connections, and query execution.
 */
/**
 * Test the MultiServerAgent implementation
 */
export declare function testMultiServerAgent(): Promise<void>;
/**
 * Test with minimal configuration (no external servers)
 */
export declare function testMinimalAgent(): Promise<void>;
/**
 * Run all agent tests
 */
export declare function runAllAgentTests(): Promise<void>;
//# sourceMappingURL=test-agent.d.ts.map