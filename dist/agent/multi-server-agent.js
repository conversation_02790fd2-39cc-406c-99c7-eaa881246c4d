/**
 * Multi-server MCP Agent implementation
 *
 * This class integrates the mcp-use library's MCPAgent with our OpenAI client
 * and configuration system to provide a unified interface for interacting
 * with multiple MCP servers.
 */
import { MCPAgent } from 'mcp-use';
import { ChatOpenAI } from '@langchain/openai';
import { MCPClientFactory } from '@/config/client-factory.js';
import { OpenAIClient } from '@/llm/openai-client.js';
import { ServerManager, createServerManager } from '@/config/server-manager.js';
/**
 * Multi-server MCP Agent that combines OpenAI LLM with multiple MCP servers
 */
export class MultiServerAgent {
    config;
    clientFactory;
    openaiClient;
    serverManager;
    mcpAgent = null;
    langchainLLM = null;
    initialized = false;
    constructor(config, openaiClient) {
        this.config = config;
        this.openaiClient = openaiClient;
        this.clientFactory = new MCPClientFactory(config);
        this.serverManager = createServerManager(config.serverManager);
    }
    /**
     * Initialize the agent with MCP servers and LLM
     */
    async initialize() {
        if (this.initialized) {
            return;
        }
        try {
            console.log('🔧 Initializing Multi-Server Agent...');
            // Initialize server manager first
            await this.serverManager.initialize(this.config.servers);
            // Create MCP client with all configured servers
            const mcpClient = this.clientFactory.createClient();
            console.log(`📡 Connected to ${this.clientFactory.getEnabledServers().length} MCP servers`);
            // Create LangChain-compatible OpenAI client for mcp-use integration
            const llmConfig = {
                modelName: this.config.llm.model,
                openAIApiKey: this.config.llm.apiKey,
            };
            // Only add optional properties if they are defined
            if (this.config.llm.temperature !== undefined) {
                llmConfig.temperature = this.config.llm.temperature;
            }
            if (this.config.llm.maxTokens !== undefined) {
                llmConfig.maxTokens = this.config.llm.maxTokens;
            }
            if (this.config.llm.baseURL) {
                llmConfig.configuration = { baseURL: this.config.llm.baseURL };
            }
            if (this.config.llm.organization) {
                llmConfig.configuration = {
                    ...llmConfig.configuration,
                    organization: this.config.llm.organization
                };
            }
            this.langchainLLM = new ChatOpenAI(llmConfig);
            // Create MCPAgent with optimized server manager configuration
            const serverManagerConfig = this.serverManager.getMCPAgentConfig();
            const agentConfig = {
                llm: this.langchainLLM,
                client: mcpClient,
                // Apply optimized server manager settings
                ...serverManagerConfig,
            };
            // Only add optional properties if they are defined
            if (this.config.agent.maxSteps !== undefined) {
                agentConfig.maxSteps = this.config.agent.maxSteps;
            }
            if (this.config.agent.verbose !== undefined) {
                agentConfig.verbose = this.config.agent.verbose;
            }
            this.mcpAgent = new MCPAgent(agentConfig);
            console.log('✅ Multi-Server Agent initialized successfully');
            console.log(`🔧 Server Manager: ${serverManagerConfig.useServerManager ? 'Enabled' : 'Disabled'}`);
            console.log(`📊 Max Concurrent Servers: ${serverManagerConfig.maxConcurrentServers}`);
            console.log(`⏱️ Server Startup Timeout: ${serverManagerConfig.serverStartupTimeout}s`);
            this.initialized = true;
        }
        catch (error) {
            console.error('❌ Failed to initialize Multi-Server Agent:', error);
            throw new Error(`Agent initialization failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Run a query using the multi-server agent
     */
    async run(query, options = {}) {
        if (!this.initialized) {
            await this.initialize();
        }
        if (!this.mcpAgent) {
            throw new Error('Agent not properly initialized');
        }
        const startTime = Date.now();
        const maxSteps = options.maxSteps || this.config.agent.maxSteps;
        try {
            console.log(`🤖 Running query: "${query}"`);
            console.log(`📊 Max steps: ${maxSteps}, Timeout: ${options.timeout || this.config.agent.timeout}ms`);
            // Run the query through MCPAgent
            const result = await this.mcpAgent.run(query, maxSteps);
            const executionTime = Date.now() - startTime;
            console.log(`✅ Query completed in ${executionTime}ms`);
            return {
                response: result,
                steps: maxSteps || this.config.agent.maxSteps || 10, // MCPAgent doesn't return actual steps taken
                executionTime,
                toolsUsed: [], // TODO: Extract from MCPAgent execution logs
                warnings: []
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ Query failed after ${executionTime}ms:`, error);
            throw new Error(`Agent execution failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Run a streaming query (using our OpenAI client for streaming)
     */
    async runStream(query, options = {}, onChunk) {
        if (!this.initialized) {
            await this.initialize();
        }
        const startTime = Date.now();
        try {
            console.log(`🌊 Running streaming query: "${query}"`);
            // For streaming, we'll use our OpenAI client directly
            // TODO: In future versions, integrate with MCPAgent streaming when available
            const messages = [
                {
                    role: 'system',
                    content: `You are a helpful AI assistant with access to multiple MCP servers. 
                   Available servers: ${this.clientFactory.getEnabledServers().map(s => s.id).join(', ')}.
                   ${options.context || ''}`
                },
                {
                    role: 'user',
                    content: query
                }
            ];
            let fullResponse = '';
            const streamOptions = {};
            if (this.config.llm.temperature !== undefined) {
                streamOptions.temperature = this.config.llm.temperature;
            }
            if (this.config.llm.maxTokens !== undefined) {
                streamOptions.maxTokens = this.config.llm.maxTokens;
            }
            const streamGenerator = this.openaiClient.streamResponse(messages, streamOptions);
            for await (const chunk of streamGenerator) {
                fullResponse += chunk;
                if (onChunk) {
                    onChunk(chunk);
                }
            }
            const executionTime = Date.now() - startTime;
            console.log(`✅ Streaming query completed in ${executionTime}ms`);
            return {
                response: fullResponse,
                steps: 1, // Direct LLM call
                executionTime,
                toolsUsed: ['openai-direct'], // Indicate direct OpenAI usage
                warnings: ['Streaming mode uses direct OpenAI client, MCP tools not available']
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ Streaming query failed after ${executionTime}ms:`, error);
            throw new Error(`Streaming execution failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Get information about available servers and tools
     */
    async getServerInfo() {
        const allServers = this.config.servers;
        const enabledServers = this.clientFactory.getEnabledServers();
        return {
            servers: allServers.map(server => ({
                id: server.id,
                name: server.name,
                description: server.description || 'No description available',
                enabled: server.enabled,
                connectionType: server.connectionType
            })),
            totalServers: allServers.length,
            enabledServers: enabledServers.length
        };
    }
    /**
     * Test connection to all enabled servers
     */
    async testConnections() {
        if (!this.initialized) {
            await this.initialize();
        }
        const enabledServers = this.clientFactory.getEnabledServers();
        const successful = [];
        const failed = [];
        console.log(`🔍 Testing connections to ${enabledServers.length} servers...`);
        for (const server of enabledServers) {
            try {
                await this.clientFactory.createSession(server.id, true);
                successful.push(server.id);
                console.log(`✅ ${server.id}: Connected successfully`);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                failed.push({ serverId: server.id, error: errorMessage });
                console.log(`❌ ${server.id}: Connection failed - ${errorMessage}`);
            }
        }
        return { successful, failed };
    }
    /**
     * Get server performance metrics
     */
    getServerMetrics() {
        return this.serverManager.getServerMetrics();
    }
    /**
     * Get server manager configuration
     */
    getServerManagerConfig() {
        return this.serverManager.getConfig();
    }
    /**
     * Gracefully shutdown the agent and close all connections
     */
    async shutdown() {
        console.log('🔄 Shutting down Multi-Server Agent...');
        try {
            await this.serverManager.shutdown();
            await this.clientFactory.closeAll();
            this.initialized = false;
            console.log('✅ Agent shutdown completed');
        }
        catch (error) {
            console.error('❌ Error during shutdown:', error);
            throw error;
        }
    }
    /**
     * Check if the agent is initialized and ready
     */
    isReady() {
        return this.initialized && this.mcpAgent !== null;
    }
    /**
     * Get the current configuration
     */
    getConfig() {
        return { ...this.config }; // Return a copy to prevent mutations
    }
}
/**
 * Factory function to create a MultiServerAgent with proper configuration
 */
export async function createMultiServerAgent(config, openaiClient) {
    const agent = new MultiServerAgent(config, openaiClient);
    if (config.agent.autoInitialize) {
        await agent.initialize();
    }
    return agent;
}
//# sourceMappingURL=multi-server-agent.js.map