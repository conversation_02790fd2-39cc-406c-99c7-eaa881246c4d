/**
 * OpenAI client implementation with AI SDK integration
 */
import { type CoreMessage } from 'ai';
import type { LLMConfig } from '@/config/types';
/**
 * OpenAI client wrapper with configuration and error handling
 */
export declare class OpenAIClient {
    private config;
    private model;
    constructor(config: LLMConfig);
    /**
     * Generate a single response from the LLM
     */
    generateResponse(messages: CoreMessage[], options?: {
        temperature?: number;
        maxTokens?: number;
        systemPrompt?: string;
    }): Promise<string>;
    /**
     * Stream a response from the LLM
     */
    streamResponse(messages: CoreMessage[], options?: {
        temperature?: number;
        maxTokens?: number;
        systemPrompt?: string;
    }): AsyncGenerator<string, void, void>;
    /**
     * Get the underlying AI SDK model for advanced usage
     */
    getModel(): import("ai").LanguageModelV1;
    /**
     * Get current configuration
     */
    getConfig(): Readonly<LLMConfig & {
        apiKey: string;
    }>;
    /**
     * Update configuration (creates new client instance)
     */
    updateConfig(newConfig: Partial<LLMConfig>): OpenAIClient;
    /**
     * Test the connection to OpenAI
     */
    testConnection(): Promise<boolean>;
    /**
     * Handle and transform errors
     */
    private handleError;
}
/**
 * Create an OpenAI client with retry logic
 */
export declare function createOpenAIClient(config: LLMConfig): Promise<OpenAIClient>;
/**
 * Utility function to convert string messages to CoreMessage format
 */
export declare function createMessage(role: 'user' | 'assistant' | 'system', content: string): CoreMessage;
/**
 * Utility function to create a conversation from string array
 */
export declare function createConversation(messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
}>): CoreMessage[];
//# sourceMappingURL=openai-client.d.ts.map