{"version": 3, "file": "factory.js", "sourceRoot": "", "sources": ["../../src/llm/factory.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAEtE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAE1D;;GAEG;AACH,MAAM,OAAO,UAAU;IACb,MAAM,CAAC,QAAQ,CAAa;IAC5B,OAAO,GAA8B,IAAI,GAAG,EAAE,CAAC;IAEvD,gBAAuB,CAAC;IAExB;;OAEG;IACH,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YACzB,UAAU,CAAC,QAAQ,GAAG,IAAI,UAAU,EAAE,CAAC;QACzC,CAAC;QACD,OAAO,UAAU,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAiB,EAAE,QAAiB;QAClD,MAAM,GAAG,GAAG,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;YACtC,qCAAqC;YACrC,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,cAAc,EAAE,CAAC;YAChD,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,MAAM,CAAC;YAChB,CAAC;YACD,kCAAkC;YAClC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC3B,CAAC;QAED,oBAAoB;QACpB,MAAM,MAAM,GAAG,MAAM,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC9B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,MAAiB;QAClC,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAgB;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,MAAiB;QACxC,MAAM,QAAQ,GAAG;YACf,MAAM,CAAC,KAAK,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK;YACxC,MAAM,CAAC,OAAO,IAAI,SAAS;YAC3B,MAAM,CAAC,YAAY,IAAI,SAAS;SACjC,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,aAAa;IAC3B,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IAEpD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,KAAK,CAAC,0CAA0C,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC;IACvF,CAAC;IAED,MAAM,MAAM,GAAc;QACxB,MAAM;QACN,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK;QAC9D,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;YAC5C,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAC/C,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW;QAClC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;YACzC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,EAAE,CAAC;YAChD,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS;QAChC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;YAC3C,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;YACjD,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU;QACjC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;YAC3C,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;YACjD,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU;KAClC,CAAC;IAEF,6CAA6C;IAC7C,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;IACtD,IAAI,OAAO,EAAE,CAAC;QACZ,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IACD,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IAC/D,IAAI,YAAY,EAAE,CAAC;QACjB,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,MAAiB;IACjD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,IAAI,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC;QAC3F,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;QAC3D,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;QAC7D,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACtD,CAAC;IAED,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;QAC7D,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACtD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,MAA0B;IACxD,MAAM,UAAU,GAAc;QAC5B,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE;QACnE,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK;QAC/C,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,cAAc,CAAC,GAAG,CAAC,WAAW;QACjE,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,cAAc,CAAC,GAAG,CAAC,SAAS;QAC3D,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,cAAc,CAAC,GAAG,CAAC,UAAU;QAC9D,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,cAAc,CAAC,GAAG,CAAC,UAAU;KAC/D,CAAC;IAEF,6CAA6C;IAC7C,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;IACxE,IAAI,OAAO,EAAE,CAAC;QACZ,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,CAAC;IACD,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IACtF,IAAI,YAAY,EAAE,CAAC;QACjB,UAAU,CAAC,YAAY,GAAG,YAAY,CAAC;IACzC,CAAC;IAED,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAC9B,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe,CAAC,MAA2B;IAC/D,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;IACzC,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;IACrE,OAAO,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACtC,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,eAAe,EAAE,CAAC;QACvC,OAAO,MAAM,CAAC,cAAc,EAAE,CAAC;IACjC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC"}