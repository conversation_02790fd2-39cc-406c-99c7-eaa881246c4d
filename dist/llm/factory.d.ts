/**
 * LLM factory for creating and managing OpenAI clients
 */
import { OpenAIClient } from './openai-client.js';
import type { LLMConfig } from '@/config/types';
/**
 * LLM factory class for managing OpenAI client instances
 */
export declare class LLMFactory {
    private static instance;
    private clients;
    private constructor();
    /**
     * Get singleton instance
     */
    static getInstance(): LLMFactory;
    /**
     * Create or get cached OpenAI client
     */
    getClient(config: LLMConfig, cacheKey?: string): Promise<OpenAIClient>;
    /**
     * Create a new client without caching
     */
    createClient(config: LLMConfig): Promise<OpenAIClient>;
    /**
     * Remove client from cache
     */
    removeClient(cacheKey: string): boolean;
    /**
     * Clear all cached clients
     */
    clearCache(): void;
    /**
     * Get number of cached clients
     */
    getCacheSize(): number;
    /**
     * Generate cache key from config
     */
    private generateCacheKey;
}
/**
 * Load LLM configuration from environment variables
 */
export declare function loadLLMConfig(): LLMConfig;
/**
 * Validate LLM configuration
 */
export declare function validateLLMConfig(config: LLMConfig): void;
/**
 * Create LLM configuration with validation
 */
export declare function createLLMConfig(config: Partial<LLMConfig>): LLMConfig;
/**
 * Convenience function to get a ready-to-use OpenAI client
 */
export declare function getOpenAIClient(config?: Partial<LLMConfig>): Promise<OpenAIClient>;
/**
 * Test OpenAI connection with current environment
 */
export declare function testOpenAIConnection(): Promise<boolean>;
//# sourceMappingURL=factory.d.ts.map